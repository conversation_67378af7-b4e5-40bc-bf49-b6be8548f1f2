# =============================================================================
# DAY TRADING SIMPLE - VERSIÓN ROBUSTA Y FUNCIONAL
# =============================================================================
# Estrategia simplificada que funciona con datos limitados
# Enfoque en indicadores esenciales y gestión de capital estricta
# =============================================================================

library(quantmod)
library(TTR)

cat("⚡ DAY TRADING SIMPLE\n")
cat("====================\n")
cat("Versión robusta para datos limitados\n\n")

# =============================================================================
# CONFIGURACIÓN SIMPLIFICADA
# =============================================================================

CONFIG_DAY <- list(
  # TUS REGLAS DE GESTIÓN
  capital_total = 10000,               # Ajustar a tu capital real
  capital_utilizable_pct = 0.10,       # 10% del capital
  riesgo_por_operacion_pct = 0.02,     # 2% del capital total
  stop_loss_pct = 0.20,                # 20% stop loss
  take_profit_pct = 0.60,              # 60% take profit
  max_operaciones_dia = 3,             # Máximo 3 operaciones
  
  # INDICADORES SIMPLIFICADOS
  ma_rapida = 5,                       # MA muy rápida
  ma_lenta = 15,                       # MA rápida
  rsi_periodo = 10,                    # RSI más sensible
  rsi_min = 40,                        # RSI mínimo
  rsi_max = 75,                        # RSI máximo
  
  # PARES A ANALIZAR
  pares = c("EURUSD=X", "GBPUSD=X", "USDJPY=X", "AUDUSD=X")
)

cat("📋 CONFIGURACIÓN DAY TRADING:\n")
cat("=============================\n")
cat("Capital total:", CONFIG_DAY$capital_total, "€\n")
cat("Capital utilizable:", CONFIG_DAY$capital_total * CONFIG_DAY$capital_utilizable_pct, "€\n")
cat("Riesgo por operación:", CONFIG_DAY$capital_total * CONFIG_DAY$riesgo_por_operacion_pct, "€\n")
cat("Stop Loss:", CONFIG_DAY$stop_loss_pct * 100, "%\n")
cat("Take Profit:", CONFIG_DAY$take_profit_pct * 100, "%\n")
cat("Máximo operaciones/día:", CONFIG_DAY$max_operaciones_dia, "\n\n")

# =============================================================================
# FUNCIÓN PARA ANALIZAR UN PAR
# =============================================================================

analizar_par_day <- function(simbolo, nombre_par) {
  cat("🔍 Analizando", nombre_par, "...")
  
  tryCatch({
    # Obtener datos recientes
    fecha_fin <- Sys.Date()
    fecha_inicio <- fecha_fin - 90  # 3 meses
    
    datos <- getSymbols(simbolo, 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) < 30) {
      cat(" ❌ Sin datos\n")
      return(NULL)
    }
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos <- na.omit(datos)
    
    # Calcular indicadores simples
    precios <- Cl(datos)
    ma_rapida <- SMA(precios, n = CONFIG_DAY$ma_rapida)
    ma_lenta <- SMA(precios, n = CONFIG_DAY$ma_lenta)
    rsi <- RSI(precios, n = CONFIG_DAY$rsi_periodo)
    
    # Crear dataframe
    df <- data.frame(
      Fecha = index(datos),
      Precio = as.numeric(precios),
      MA_Rapida = as.numeric(ma_rapida),
      MA_Lenta = as.numeric(ma_lenta),
      RSI = as.numeric(rsi),
      High = as.numeric(Hi(datos)),
      Low = as.numeric(Lo(datos))
    )
    
    df <- df[complete.cases(df), ]
    
    if (nrow(df) < 10) {
      cat(" ❌ Datos insuficientes\n")
      return(NULL)
    }
    
    # Analizar últimos días para encontrar señales
    señales <- detectar_señales_simples(df, nombre_par)
    
    cat(" ✅", nrow(señales), "señales\n")
    return(señales)
    
  }, error = function(e) {
    cat(" ❌ Error\n")
    return(NULL)
  })
}

# =============================================================================
# FUNCIÓN PARA DETECTAR SEÑALES SIMPLES
# =============================================================================

detectar_señales_simples <- function(datos, nombre_par) {
  señales <- data.frame()
  
  # Analizar últimos 10 días para encontrar señales
  n_start <- max(1, nrow(datos) - 10)
  
  for (i in (n_start + 1):nrow(datos)) {
    if (i <= 1) next
    
    actual <- datos[i, ]
    anterior <- datos[i-1, ]
    
    # Verificar datos válidos
    if (any(is.na(c(actual$Precio, actual$MA_Rapida, actual$MA_Lenta, actual$RSI)))) {
      next
    }
    
    # =============================================================================
    # SEÑAL DE COMPRA SIMPLE
    # =============================================================================
    
    # Condición 1: Cruce alcista de medias
    cruce_alcista <- (anterior$MA_Rapida <= anterior$MA_Lenta) && 
                     (actual$MA_Rapida > actual$MA_Lenta)
    
    # Condición 2: RSI favorable
    rsi_favorable <- actual$RSI >= CONFIG_DAY$rsi_min && 
                     actual$RSI <= CONFIG_DAY$rsi_max
    
    # Condición 3: Precio arriba de MA lenta
    precio_ok <- actual$Precio > actual$MA_Lenta
    
    # Condición 4: Tendencia alcista (MA rápida subiendo)
    tendencia_ok <- if (i > 2) {
      actual$MA_Rapida > datos$MA_Rapida[i-1]
    } else {
      TRUE
    }
    
    # REGISTRAR SEÑAL DE COMPRA
    if (cruce_alcista && rsi_favorable && precio_ok && tendencia_ok) {
      señal <- data.frame(
        Par = nombre_par,
        Fecha = actual$Fecha,
        Tipo = "COMPRA",
        Precio = actual$Precio,
        MA_Rapida = actual$MA_Rapida,
        MA_Lenta = actual$MA_Lenta,
        RSI = actual$RSI,
        Calidad = "ALTA",
        Cruce = cruce_alcista,
        stringsAsFactors = FALSE
      )
      
      señales <- rbind(señales, señal)
    }
    
    # Señal de calidad media (solo tendencia alcista sin cruce)
    else if (!cruce_alcista && rsi_favorable && precio_ok && tendencia_ok && 
             actual$MA_Rapida > actual$MA_Lenta) {
      señal <- data.frame(
        Par = nombre_par,
        Fecha = actual$Fecha,
        Tipo = "COMPRA",
        Precio = actual$Precio,
        MA_Rapida = actual$MA_Rapida,
        MA_Lenta = actual$MA_Lenta,
        RSI = actual$RSI,
        Calidad = "MEDIA",
        Cruce = FALSE,
        stringsAsFactors = FALSE
      )
      
      señales <- rbind(señales, señal)
    }
  }
  
  return(señales)
}

# =============================================================================
# FUNCIÓN PARA CALCULAR NIVELES DE TRADING
# =============================================================================

calcular_niveles_trading <- function(precio_entrada) {
  # Calcular stop loss y take profit
  stop_loss <- precio_entrada * (1 - CONFIG_DAY$stop_loss_pct)
  take_profit <- precio_entrada * (1 + CONFIG_DAY$take_profit_pct)
  
  # Calcular tamaño de posición
  capital_utilizable <- CONFIG_DAY$capital_total * CONFIG_DAY$capital_utilizable_pct
  riesgo_euros <- CONFIG_DAY$capital_total * CONFIG_DAY$riesgo_por_operacion_pct
  
  # Riesgo por pip
  riesgo_pips <- precio_entrada - stop_loss
  
  # Tamaño en lotes (para forex)
  if (riesgo_pips > 0) {
    tamaño_lotes <- riesgo_euros / (riesgo_pips * 100000)
    tamaño_lotes <- round(tamaño_lotes, 2)
    tamaño_lotes <- max(0.01, tamaño_lotes)  # Mínimo 0.01 lotes
  } else {
    tamaño_lotes <- 0.01
  }
  
  return(list(
    precio_entrada = precio_entrada,
    stop_loss = stop_loss,
    take_profit = take_profit,
    tamaño_lotes = tamaño_lotes,
    riesgo_euros = riesgo_euros,
    ganancia_potencial = tamaño_lotes * (take_profit - precio_entrada) * 100000
  ))
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE ESCANEO
# =============================================================================

escanear_oportunidades_day <- function() {
  cat("🎯 ESCANEANDO OPORTUNIDADES DE DAY TRADING\n")
  cat("==========================================\n")
  cat("Hora:", format(Sys.time(), "%H:%M:%S"), "\n")
  cat("Fecha:", format(Sys.Date(), "%Y-%m-%d"), "\n\n")
  
  todas_señales <- data.frame()
  
  # Analizar cada par
  for (i in 1:length(CONFIG_DAY$pares)) {
    simbolo <- CONFIG_DAY$pares[i]
    nombre_par <- gsub("=X", "", simbolo)
    nombre_par <- gsub("USD", "/USD", nombre_par)
    nombre_par <- gsub("JPY", "/JPY", nombre_par)
    nombre_par <- gsub("EUR/USD", "EUR/USD", nombre_par)
    nombre_par <- gsub("GBP/USD", "GBP/USD", nombre_par)
    
    señales_par <- analizar_par_day(simbolo, nombre_par)
    
    if (!is.null(señales_par) && nrow(señales_par) > 0) {
      todas_señales <- rbind(todas_señales, señales_par)
    }
    
    Sys.sleep(0.3)  # Pausa para no sobrecargar
  }
  
  # Mostrar resultados
  mostrar_resultados_day(todas_señales)
}

# =============================================================================
# FUNCIÓN PARA MOSTRAR RESULTADOS
# =============================================================================

mostrar_resultados_day <- function(señales) {
  cat("\n📊 RESULTADOS DEL ESCANEO\n")
  cat("=========================\n")
  
  if (nrow(señales) == 0) {
    cat("⏸️ NO HAY SEÑALES DE DAY TRADING HOY\n")
    cat("💡 Recomendaciones:\n")
    cat("- Esperar 30 minutos y volver a escanear\n")
    cat("- Revisar gráficos de 15 minutos manualmente\n")
    cat("- Verificar noticias económicas\n")
    return()
  }
  
  # Filtrar y ordenar señales
  señales_alta <- señales[señales$Calidad == "ALTA", ]
  señales_media <- señales[señales$Calidad == "MEDIA", ]
  
  cat("🚀 Señales de ALTA calidad:", nrow(señales_alta), "\n")
  cat("📈 Señales de MEDIA calidad:", nrow(señales_media), "\n\n")
  
  # Mostrar mejores oportunidades
  if (nrow(señales_alta) > 0) {
    cat("🏆 MEJORES OPORTUNIDADES (ALTA CALIDAD):\n")
    cat("========================================\n")
    
    for (i in 1:min(3, nrow(señales_alta))) {
      señal <- señales_alta[i, ]
      niveles <- calcular_niveles_trading(señal$Precio)
      
      cat("📈", señal$Par, "- COMPRAR\n")
      cat("   Fecha señal:", as.character(señal$Fecha), "\n")
      cat("   Precio actual:", round(señal$Precio, 5), "\n")
      cat("   RSI:", round(señal$RSI, 2), "\n")
      cat("   Cruce de medias:", ifelse(señal$Cruce, "✅ SÍ", "❌ No"), "\n")
      
      cat("   💰 NIVELES PARA XTB:\n")
      cat("      Tamaño:", niveles$tamaño_lotes, "lotes\n")
      cat("      Stop Loss:", round(niveles$stop_loss, 5), "\n")
      cat("      Take Profit:", round(niveles$take_profit, 5), "\n")
      cat("      Riesgo:", round(niveles$riesgo_euros, 2), "€\n")
      cat("      Ganancia potencial:", round(niveles$ganancia_potencial, 2), "€\n\n")
    }
    
    cat("📋 CHECKLIST PARA OPERAR:\n")
    cat("□ Verificar que no has hecho 3 operaciones hoy\n")
    cat("□ Abrir XTB en timeframe 15 minutos\n")
    cat("□ Confirmar cruce de medias móviles visualmente\n")
    cat("□ Verificar RSI entre 40-75\n")
    cat("□ Ejecutar compra con niveles calculados\n")
    cat("□ Anotar operación en registro\n")
    
  } else if (nrow(señales_media) > 0) {
    cat("📈 OPORTUNIDADES DE CALIDAD MEDIA:\n")
    cat("==================================\n")
    
    for (i in 1:min(2, nrow(señales_media))) {
      señal <- señales_media[i, ]
      cat("•", señal$Par, "- Tendencia alcista | RSI:", round(señal$RSI, 2), "\n")
    }
    
    cat("\n💡 Estas señales son menos seguras\n")
    cat("⚠️ Considerar solo si tienes experiencia\n")
  }
  
  cat("\n⏰ PRÓXIMO ESCANEO: En 30 minutos\n")
  cat("🎯 Recordatorio: Máximo 3 operaciones por día\n")
}

# =============================================================================
# EJECUCIÓN PRINCIPAL
# =============================================================================

cat("🎯 Iniciando escaneo de day trading...\n\n")
escanear_oportunidades_day()

cat("\n⚡ DAY TRADING SIMPLE COMPLETADO\n")
cat("================================\n")
cat("💡 Ejecutar cada 30 minutos durante horario de trading\n")
cat("🕐 Horario recomendado: 8:00 - 20:00\n")
cat("🎯 Enfocarse en señales de ALTA calidad\n")
cat("⚠️ Máximo 3 operaciones por día\n")
