# =============================================================================
# DAY TRADING HOY - SOLO SEÑALES DE HOY
# =============================================================================
# Versión específica que solo busca señales del día actual
# Evita confusión con señales históricas
# =============================================================================

library(quantmod)
library(TTR)

cat("📅 DAY TRADING HOY - SEÑALES DEL DÍA ACTUAL\n")
cat("===========================================\n")
cat("Fecha:", format(Sys.Date(), "%Y-%m-%d"), "\n")
cat("Hora:", format(Sys.time(), "%H:%M:%S"), "\n\n")

# =============================================================================
# CONFIGURACIÓN
# =============================================================================

CONFIG_HOY <- list(
  capital_total = 10000,
  capital_utilizable_pct = 0.10,
  riesgo_por_operacion_pct = 0.02,
  stop_loss_pct = 0.02,        # 2% para day trading
  take_profit_pct = 0.03,      # 3% para day trading
  max_operaciones_dia = 3,
  
  # Indicadores rápidos para day trading
  ma_rapida = 5,
  ma_lenta = 15,
  rsi_periodo = 10,
  rsi_min = 40,
  rsi_max = 75,
  
  pares = c("EURUSD=X", "GBPUSD=X", "USDJPY=X", "AUDUSD=X")
)

# =============================================================================
# FUNCIÓN PARA ANALIZAR SEÑALES DE HOY
# =============================================================================

analizar_señales_hoy <- function(simbolo, nombre_par) {
  cat("🔍", nombre_par, "...")
  
  tryCatch({
    # Obtener datos recientes
    fecha_fin <- Sys.Date()
    fecha_inicio <- fecha_fin - 30  # 1 mes para tener suficientes datos
    
    datos <- getSymbols(simbolo, 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) < 20) {
      cat(" ❌ Sin datos\n")
      return(NULL)
    }
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos <- na.omit(datos)
    
    # Calcular indicadores
    precios <- Cl(datos)
    ma_rapida <- SMA(precios, n = CONFIG_HOY$ma_rapida)
    ma_lenta <- SMA(precios, n = CONFIG_HOY$ma_lenta)
    rsi <- RSI(precios, n = CONFIG_HOY$rsi_periodo)
    
    # Crear dataframe
    df <- data.frame(
      Fecha = index(datos),
      Precio = as.numeric(precios),
      MA_Rapida = as.numeric(ma_rapida),
      MA_Lenta = as.numeric(ma_lenta),
      RSI = as.numeric(rsi)
    )
    
    df <- df[complete.cases(df), ]
    
    if (nrow(df) < 10) {
      cat(" ❌ Datos insuficientes\n")
      return(NULL)
    }
    
    # Buscar señal de HOY específicamente
    señal_hoy <- detectar_señal_hoy(df, nombre_par)
    
    if (!is.null(señal_hoy)) {
      cat(" ✅ SEÑAL HOY\n")
    } else {
      cat(" ⏸️ Sin señal hoy\n")
    }
    
    return(señal_hoy)
    
  }, error = function(e) {
    cat(" ❌ Error\n")
    return(NULL)
  })
}

# =============================================================================
# FUNCIÓN PARA DETECTAR SEÑAL ESPECÍFICA DE HOY
# =============================================================================

detectar_señal_hoy <- function(datos, nombre_par) {
  fecha_hoy <- Sys.Date()
  fecha_ayer <- fecha_hoy - 1
  
  # Buscar datos de hoy y ayer
  datos_hoy <- datos[as.Date(datos$Fecha) == fecha_hoy, ]
  datos_ayer <- datos[as.Date(datos$Fecha) == fecha_ayer, ]
  
  # Si no hay datos de hoy, buscar el último día disponible
  if (nrow(datos_hoy) == 0) {
    datos_hoy <- datos[nrow(datos), ]
    if (nrow(datos) > 1) {
      datos_ayer <- datos[nrow(datos) - 1, ]
    } else {
      return(NULL)
    }
  }
  
  if (nrow(datos_ayer) == 0 && nrow(datos) > 1) {
    datos_ayer <- datos[nrow(datos) - 1, ]
  }
  
  if (nrow(datos_hoy) == 0 || nrow(datos_ayer) == 0) {
    return(NULL)
  }
  
  # Verificar datos válidos
  if (any(is.na(c(datos_hoy$Precio, datos_hoy$MA_Rapida, datos_hoy$MA_Lenta, datos_hoy$RSI)))) {
    return(NULL)
  }
  
  # =============================================================================
  # DETECTAR SEÑAL DE COMPRA DE HOY
  # =============================================================================
  
  # Condición 1: Cruce alcista (ayer vs hoy)
  cruce_alcista <- (datos_ayer$MA_Rapida <= datos_ayer$MA_Lenta) && 
                   (datos_hoy$MA_Rapida > datos_hoy$MA_Lenta)
  
  # Condición 2: RSI favorable
  rsi_favorable <- datos_hoy$RSI >= CONFIG_HOY$rsi_min && 
                   datos_hoy$RSI <= CONFIG_HOY$rsi_max
  
  # Condición 3: Precio arriba de MA lenta
  precio_ok <- datos_hoy$Precio > datos_hoy$MA_Lenta
  
  # Condición 4: Tendencia alcista actual
  tendencia_alcista <- datos_hoy$MA_Rapida > datos_hoy$MA_Lenta
  
  # Evaluar calidad de la señal
  if (cruce_alcista && rsi_favorable && precio_ok) {
    calidad <- "ALTA"
  } else if (tendencia_alcista && rsi_favorable && precio_ok) {
    calidad <- "MEDIA"
  } else {
    return(NULL)  # No hay señal válida
  }
  
  # Crear señal
  señal <- data.frame(
    Par = nombre_par,
    Fecha = datos_hoy$Fecha,
    Precio = datos_hoy$Precio,
    MA_Rapida = datos_hoy$MA_Rapida,
    MA_Lenta = datos_hoy$MA_Lenta,
    RSI = datos_hoy$RSI,
    Calidad = calidad,
    Cruce = cruce_alcista,
    Dias_Antiguedad = 0,  # Es de hoy
    stringsAsFactors = FALSE
  )
  
  return(señal)
}

# =============================================================================
# FUNCIÓN PARA CALCULAR NIVELES REALISTAS
# =============================================================================

calcular_niveles_day_trading <- function(precio_entrada) {
  # Niveles realistas para day trading
  stop_loss <- precio_entrada * (1 - CONFIG_HOY$stop_loss_pct)
  take_profit <- precio_entrada * (1 + CONFIG_HOY$take_profit_pct)
  
  # Calcular tamaño de posición
  riesgo_euros <- CONFIG_HOY$capital_total * CONFIG_HOY$riesgo_por_operacion_pct
  riesgo_pips <- precio_entrada - stop_loss
  
  if (riesgo_pips > 0) {
    tamaño_lotes <- riesgo_euros / (riesgo_pips * 100000)
    tamaño_lotes <- round(tamaño_lotes, 2)
    tamaño_lotes <- max(0.01, tamaño_lotes)
  } else {
    tamaño_lotes <- 0.01
  }
  
  ganancia_potencial <- tamaño_lotes * (take_profit - precio_entrada) * 100000
  
  return(list(
    precio_entrada = precio_entrada,
    stop_loss = stop_loss,
    take_profit = take_profit,
    tamaño_lotes = tamaño_lotes,
    riesgo_euros = riesgo_euros,
    ganancia_potencial = ganancia_potencial
  ))
}

# =============================================================================
# FUNCIÓN PRINCIPAL
# =============================================================================

escanear_hoy <- function() {
  cat("🎯 BUSCANDO SEÑALES DE DAY TRADING DE HOY\n")
  cat("=========================================\n\n")
  
  señales_hoy <- list()
  
  # Analizar cada par
  for (i in 1:length(CONFIG_HOY$pares)) {
    simbolo <- CONFIG_HOY$pares[i]
    nombre_par <- gsub("=X", "", simbolo)
    nombre_par <- gsub("USD", "/USD", nombre_par)
    nombre_par <- gsub("JPY", "/JPY", nombre_par)
    nombre_par <- gsub("EUR/USD", "EUR/USD", nombre_par)
    nombre_par <- gsub("GBP/USD", "GBP/USD", nombre_par)
    
    señal <- analizar_señales_hoy(simbolo, nombre_par)
    
    if (!is.null(señal)) {
      señales_hoy[[length(señales_hoy) + 1]] <- señal
    }
    
    Sys.sleep(0.3)
  }
  
  # Mostrar resultados
  mostrar_resultados_hoy(señales_hoy)
}

# =============================================================================
# FUNCIÓN PARA MOSTRAR RESULTADOS
# =============================================================================

mostrar_resultados_hoy <- function(señales_hoy) {
  cat("\n📊 RESULTADOS - SEÑALES DE HOY\n")
  cat("===============================\n")
  
  if (length(señales_hoy) == 0) {
    cat("⏸️ NO HAY SEÑALES DE DAY TRADING HOY\n")
    cat("💡 Esto es normal - las señales de calidad no aparecen todos los días\n")
    cat("🔄 Volver a escanear en 30-60 minutos\n")
    cat("📱 Considerar usar el monitor en tiempo real\n")
    return()
  }
  
  # Separar por calidad
  señales_alta <- Filter(function(s) s$Calidad == "ALTA", señales_hoy)
  señales_media <- Filter(function(s) s$Calidad == "MEDIA", señales_hoy)
  
  cat("🚀 Señales ALTA calidad (HOY):", length(señales_alta), "\n")
  cat("📈 Señales MEDIA calidad (HOY):", length(señales_media), "\n\n")
  
  # Mostrar señales de alta calidad
  if (length(señales_alta) > 0) {
    cat("🏆 OPORTUNIDADES DE HOY - ALTA CALIDAD:\n")
    cat("======================================\n")
    
    for (señal in señales_alta) {
      niveles <- calcular_niveles_day_trading(señal$Precio)
      
      cat("📈", señal$Par, "- COMPRAR 🟢 HOY\n")
      cat("   Precio actual:", round(señal$Precio, 5), "\n")
      cat("   RSI:", round(señal$RSI, 2), "\n")
      cat("   Cruce de medias:", ifelse(señal$Cruce, "✅ SÍ", "❌ No"), "\n")
      
      cat("   💰 NIVELES PARA XTB (DAY TRADING):\n")
      cat("      Tamaño:", niveles$tamaño_lotes, "lotes\n")
      cat("      Stop Loss (-2%):", round(niveles$stop_loss, 5), "\n")
      cat("      Take Profit (+3%):", round(niveles$take_profit, 5), "\n")
      cat("      Riesgo:", round(niveles$riesgo_euros, 2), "€\n")
      cat("      Ganancia potencial:", round(niveles$ganancia_potencial, 2), "€\n\n")
    }
    
    cat("✅ ESTAS SEÑALES SON DE HOY - SEGURAS PARA OPERAR\n\n")
  }
  
  # Mostrar señales de calidad media
  if (length(señales_media) > 0) {
    cat("📈 OPORTUNIDADES DE HOY - CALIDAD MEDIA:\n")
    cat("=======================================\n")
    
    for (señal in señales_media) {
      cat("•", señal$Par, "- Tendencia alcista | RSI:", round(señal$RSI, 2), "\n")
    }
    
    cat("\n⚠️ Señales de calidad media - operar con precaución\n")
  }
  
  cat("📋 CHECKLIST PARA OPERAR:\n")
  cat("□ Verificar que no has hecho 3 operaciones hoy\n")
  cat("□ Abrir XTB en timeframe 15 minutos\n")
  cat("□ Confirmar señal visualmente\n")
  cat("□ Usar niveles de day trading (-2%/+3%)\n")
  cat("□ Ejecutar operación\n")
  cat("□ Anotar en registro\n")
  
  cat("\n⏰ Próximo escaneo: En 30 minutos\n")
}

# =============================================================================
# EJECUCIÓN
# =============================================================================

escanear_hoy()

cat("\n📅 DAY TRADING HOY COMPLETADO\n")
cat("=============================\n")
cat("💡 Este script solo muestra señales del día actual\n")
cat("🔄 Ejecutar cada 30-60 minutos durante el día\n")
