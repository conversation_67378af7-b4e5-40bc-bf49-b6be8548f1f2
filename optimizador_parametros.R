# =============================================================================
# OPTIMIZADOR DE PARÁMETROS PARA ESTRATEGIA DE TRADING
# =============================================================================
# Este script prueba diferentes combinaciones de parámetros para encontrar
# la configuración óptima de la estrategia
# =============================================================================

library(quantmod)
library(TTR)
library(dplyr)

cat("🔧 OPTIMIZADOR DE PARÁMETROS DE TRADING\n")
cat("======================================\n\n")

# =============================================================================
# CONFIGURACIONES A PROBAR
# =============================================================================

# Definir rangos de parámetros para probar
configuraciones <- expand.grid(
  ma_corta = c(10, 15, 20, 25),
  ma_larga = c(30, 40, 50, 60),
  rsi_entrada = c(40, 45, 50, 55),
  rsi_salida = c(70, 75, 80),
  stop_loss = c(0.01, 0.015, 0.02),
  take_profit = c(0.025, 0.03, 0.04)
)

# Filtrar combinaciones lógicas (ma_corta < ma_larga)
configuraciones <- configuraciones[configuraciones$ma_corta < configuraciones$ma_larga, ]

cat("📊 Total de configuraciones a probar:", nrow(configuraciones), "\n")
cat("Esto puede tomar varios minutos...\n\n")

# =============================================================================
# FUNCIÓN PARA PROBAR UNA CONFIGURACIÓN
# =============================================================================

probar_configuracion <- function(config, datos) {
  tryCatch({
    # Calcular indicadores con parámetros específicos
    precios <- datos$Close
    ma_corta <- SMA(precios, n = config$ma_corta)
    ma_larga <- SMA(precios, n = config$ma_larga)
    rsi <- RSI(precios, n = 14)
    
    # Crear dataframe
    df <- data.frame(
      Fecha = index(datos),
      Precio = as.numeric(precios),
      MA_Corta = as.numeric(ma_corta),
      MA_Larga = as.numeric(ma_larga),
      RSI = as.numeric(rsi)
    )
    
    df <- df[complete.cases(df), ]
    
    if (nrow(df) < 50) return(NULL)
    
    # Simular trading
    posicion_abierta <- FALSE
    precio_entrada <- 0
    operaciones <- c()
    
    for (i in 2:nrow(df)) {
      if (!posicion_abierta) {
        # Condición de entrada
        cruce_alcista <- (df$MA_Corta[i-1] <= df$MA_Larga[i-1]) && 
                         (df$MA_Corta[i] > df$MA_Larga[i])
        rsi_ok <- df$RSI[i] >= config$rsi_entrada
        
        if (cruce_alcista && rsi_ok && !is.na(df$RSI[i])) {
          posicion_abierta <- TRUE
          precio_entrada <- df$Precio[i]
        }
      } else {
        # Condiciones de salida
        precio_actual <- df$Precio[i]
        rsi_actual <- df$RSI[i]
        
        salida <- FALSE
        
        # RSI sobrecompra
        if (!is.na(rsi_actual) && rsi_actual >= config$rsi_salida) {
          salida <- TRUE
        }
        
        # Stop Loss
        if (precio_actual <= precio_entrada * (1 - config$stop_loss)) {
          salida <- TRUE
        }
        
        # Take Profit
        if (precio_actual >= precio_entrada * (1 + config$take_profit)) {
          salida <- TRUE
        }
        
        # Precio bajo MA corta
        if (precio_actual < df$MA_Corta[i]) {
          salida <- TRUE
        }
        
        if (salida) {
          rendimiento <- (precio_actual - precio_entrada) / precio_entrada
          operaciones <- c(operaciones, rendimiento)
          posicion_abierta <- FALSE
        }
      }
    }
    
    if (length(operaciones) == 0) return(NULL)
    
    # Calcular métricas
    total_ops <- length(operaciones)
    ops_ganadoras <- sum(operaciones > 0)
    tasa_exito <- ops_ganadoras / total_ops
    rendimiento_total <- sum(operaciones)
    rendimiento_promedio <- mean(operaciones)
    
    # Calcular drawdown
    rendimientos_acum <- cumsum(operaciones)
    picos <- cummax(rendimientos_acum)
    drawdowns <- rendimientos_acum - picos
    drawdown_max <- min(drawdowns)
    
    return(list(
      total_operaciones = total_ops,
      tasa_exito = tasa_exito,
      rendimiento_total = rendimiento_total,
      rendimiento_promedio = rendimiento_promedio,
      drawdown_maximo = drawdown_max
    ))
    
  }, error = function(e) {
    return(NULL)
  })
}

# =============================================================================
# OBTENER DATOS Y EJECUTAR OPTIMIZACIÓN
# =============================================================================

cat("📥 Descargando datos para optimización...\n")

# Obtener datos
datos_eurusd <- tryCatch({
  getSymbols("EURUSD=X", 
             src = "yahoo",
             from = Sys.Date() - 730,
             to = Sys.Date(),
             auto.assign = FALSE,
             warnings = FALSE)
}, error = function(e) NULL)

if (is.null(datos_eurusd)) {
  cat("❌ No se pudieron obtener datos\n")
  stop("Datos no disponibles")
}

# Limpiar datos
datos_eurusd <- na.omit(datos_eurusd)
colnames(datos_eurusd) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")

cat("✅ Datos obtenidos:", nrow(datos_eurusd), "días\n")
cat("🔄 Iniciando optimización...\n\n")

# Ejecutar optimización
resultados_optimizacion <- data.frame()

for (i in 1:min(50, nrow(configuraciones))) {  # Limitar a 50 para no tardar mucho
  config <- configuraciones[i, ]
  
  if (i %% 10 == 0) {
    cat("Progreso:", i, "/", min(50, nrow(configuraciones)), "\n")
  }
  
  resultado <- probar_configuracion(config, datos_eurusd)
  
  if (!is.null(resultado)) {
    fila_resultado <- data.frame(
      Configuracion = i,
      MA_Corta = config$ma_corta,
      MA_Larga = config$ma_larga,
      RSI_Entrada = config$rsi_entrada,
      RSI_Salida = config$rsi_salida,
      Stop_Loss = config$stop_loss * 100,
      Take_Profit = config$take_profit * 100,
      Total_Ops = resultado$total_operaciones,
      Tasa_Exito = round(resultado$tasa_exito * 100, 1),
      Rendimiento_Total = round(resultado$rendimiento_total * 100, 2),
      Rendimiento_Promedio = round(resultado$rendimiento_promedio * 100, 2),
      Drawdown_Max = round(resultado$drawdown_maximo * 100, 2)
    )
    
    resultados_optimizacion <- rbind(resultados_optimizacion, fila_resultado)
  }
}

# =============================================================================
# ANÁLISIS DE RESULTADOS
# =============================================================================

if (nrow(resultados_optimizacion) > 0) {
  cat("\n📊 RESULTADOS DE LA OPTIMIZACIÓN\n")
  cat("================================\n")
  
  # Filtrar configuraciones con al menos 3 operaciones
  resultados_filtrados <- resultados_optimizacion[resultados_optimizacion$Total_Ops >= 3, ]
  
  if (nrow(resultados_filtrados) > 0) {
    cat("Configuraciones válidas encontradas:", nrow(resultados_filtrados), "\n\n")
    
    # Ordenar por rendimiento total
    resultados_ordenados <- resultados_filtrados[order(-resultados_filtrados$Rendimiento_Total), ]
    
    cat("🏆 TOP 5 MEJORES CONFIGURACIONES (por rendimiento total):\n")
    cat("========================================================\n")
    print(head(resultados_ordenados, 5))
    
    cat("\n🎯 MEJOR CONFIGURACIÓN:\n")
    cat("=======================\n")
    mejor <- resultados_ordenados[1, ]
    cat("MA Corta:", mejor$MA_Corta, "días\n")
    cat("MA Larga:", mejor$MA_Larga, "días\n")
    cat("RSI Entrada:", mejor$RSI_Entrada, "\n")
    cat("RSI Salida:", mejor$RSI_Salida, "\n")
    cat("Stop Loss:", mejor$Stop_Loss, "%\n")
    cat("Take Profit:", mejor$Take_Profit, "%\n")
    cat("Total Operaciones:", mejor$Total_Ops, "\n")
    cat("Tasa de Éxito:", mejor$Tasa_Exito, "%\n")
    cat("Rendimiento Total:", mejor$Rendimiento_Total, "%\n")
    cat("Drawdown Máximo:", mejor$Drawdown_Max, "%\n")
    
    # Análisis adicional
    cat("\n📈 ESTADÍSTICAS GENERALES:\n")
    cat("==========================\n")
    cat("Rendimiento promedio de todas las configuraciones:", 
        round(mean(resultados_filtrados$Rendimiento_Total), 2), "%\n")
    cat("Tasa de éxito promedio:", 
        round(mean(resultados_filtrados$Tasa_Exito), 1), "%\n")
    cat("Configuraciones rentables:", 
        sum(resultados_filtrados$Rendimiento_Total > 0), "/", 
        nrow(resultados_filtrados), "\n")
    
    # Guardar resultados
    write.csv(resultados_optimizacion, "resultados_optimizacion.csv", row.names = FALSE)
    cat("\n💾 Resultados guardados en 'resultados_optimizacion.csv'\n")
    
  } else {
    cat("❌ No se encontraron configuraciones válidas\n")
    cat("Sugerencias:\n")
    cat("- Reducir el número mínimo de operaciones requeridas\n")
    cat("- Ampliar los rangos de parámetros\n")
    cat("- Usar un período de datos más largo\n")
  }
  
} else {
  cat("❌ No se obtuvieron resultados válidos\n")
  cat("Posibles causas:\n")
  cat("- Datos insuficientes\n")
  cat("- Parámetros muy restrictivos\n")
  cat("- Problemas en el cálculo de indicadores\n")
}

cat("\n✅ Optimización completada\n")
cat("==========================\n")
cat("💡 Usa los mejores parámetros encontrados en tu estrategia principal\n")
library(data.table)
result <- fread("resultados_optimizacion.csv")
