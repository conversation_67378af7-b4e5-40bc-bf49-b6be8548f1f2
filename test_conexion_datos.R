# =============================================================================
# SCRIPT DE PRUEBA: CONEXIÓN Y DESCARGA DE DATOS EUR/USD
# =============================================================================
# Este script verifica si se pueden descargar datos de EUR/USD desde Yahoo Finance
# Úsalo para probar la conexión antes de ejecutar la estrategia completa
# =============================================================================

# Cargar librerías necesarias
cat("🔍 Verificando librerías...\n")

# Lista de librerías requeridas
librerias_requeridas <- c("quantmod", "TTR", "ggplot2", "dplyr", "gridExtra", "scales")

# Verificar e instalar librerías faltantes
for (lib in librerias_requeridas) {
  if (!require(lib, character.only = TRUE, quietly = TRUE)) {
    cat("📦 Instalando librería:", lib, "\n")
    install.packages(lib, dependencies = TRUE)
    library(lib, character.only = TRUE)
  } else {
    cat("✅", lib, "- OK\n")
  }
}

cat("\n🌐 Probando conexión a Yahoo Finance...\n")

# Función de prueba de conexión
probar_conexion_eurusd <- function() {
  # Configurar fechas de prueba (últimos 30 días)
  fecha_fin <- Sys.Date()
  fecha_inicio <- fecha_fin - 30
  
  cat("Intentando descargar datos de EUR/USD...\n")
  cat("Período de prueba:", as.character(fecha_inicio), "a", as.character(fecha_fin), "\n")
  
  # Intentar descarga
  tryCatch({
    # Descargar datos de prueba
    datos_prueba <- getSymbols("EURUSD=X", 
                              src = "yahoo",
                              from = fecha_inicio,
                              to = fecha_fin,
                              auto.assign = FALSE,
                              warnings = FALSE)
    
    # Verificar que se descargaron datos
    if (is.null(datos_prueba) || nrow(datos_prueba) == 0) {
      cat("❌ No se obtuvieron datos (respuesta vacía)\n")
      return(FALSE)
    }
    
    # Mostrar información de los datos
    cat("✅ ¡Conexión exitosa!\n")
    cat("📊 Datos descargados:\n")
    cat("   - Filas:", nrow(datos_prueba), "\n")
    cat("   - Columnas:", ncol(datos_prueba), "\n")
    cat("   - Primer precio:", round(as.numeric(Cl(datos_prueba)[1]), 5), "\n")
    cat("   - Último precio:", round(as.numeric(Cl(datos_prueba)[nrow(datos_prueba)]), 5), "\n")
    
    # Mostrar muestra de datos
    cat("\n📋 Muestra de datos (últimos 5 días):\n")
    ultimos_datos <- tail(datos_prueba, 5)
    colnames(ultimos_datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    print(round(ultimos_datos[, 1:4], 5))
    
    return(TRUE)
    
  }, error = function(e) {
    cat("❌ Error en la conexión:\n")
    cat("   Mensaje:", e$message, "\n")
    
    # Diagnóstico adicional
    cat("\n🔧 Diagnóstico:\n")
    
    # Verificar conexión a internet
    cat("1. Probando conexión a internet...\n")
    test_internet <- tryCatch({
      readLines("https://www.google.com", n = 1, warn = FALSE)
      TRUE
    }, error = function(e) FALSE)
    
    if (test_internet) {
      cat("   ✅ Conexión a internet: OK\n")
    } else {
      cat("   ❌ Conexión a internet: FALLO\n")
      cat("   → Verificar conexión de red\n")
    }
    
    # Verificar acceso a Yahoo Finance
    cat("2. Probando acceso a Yahoo Finance...\n")
    test_yahoo <- tryCatch({
      readLines("https://finance.yahoo.com", n = 1, warn = FALSE)
      TRUE
    }, error = function(e) FALSE)
    
    if (test_yahoo) {
      cat("   ✅ Acceso a Yahoo Finance: OK\n")
      cat("   → El problema puede ser temporal o específico del símbolo\n")
    } else {
      cat("   ❌ Acceso a Yahoo Finance: BLOQUEADO\n")
      cat("   → Posible bloqueo por firewall o proxy\n")
    }
    
    return(FALSE)
  })
}

# Ejecutar prueba
resultado_prueba <- probar_conexion_eurusd()

# Mostrar resultado final
cat("\n", paste(rep("=", 60), collapse = ""), "\n")
if (resultado_prueba) {
  cat("🎉 RESULTADO: ¡Todo listo para ejecutar la estrategia!\n")
  cat("✅ Puedes proceder a ejecutar 'estrategia_trading_eurusd.R'\n")
} else {
  cat("⚠️ RESULTADO: Hay problemas con la conexión de datos\n")
  cat("❌ Resuelve los problemas antes de ejecutar la estrategia\n")
  
  cat("\n💡 Soluciones sugeridas:\n")
  cat("1. Verificar conexión a internet\n")
  cat("2. Intentar más tarde (problema temporal de Yahoo Finance)\n")
  cat("3. Configurar proxy si estás en una red corporativa\n")
  cat("4. Usar VPN si hay restricciones geográficas\n")
  cat("5. Probar con otro símbolo: 'EUR=X' o 'EURUSD'\n")
}

cat(paste(rep("=", 60), collapse = ""), "\n")

# Función adicional para probar símbolos alternativos
probar_simbolos_alternativos <- function() {
  cat("\n🔄 Probando símbolos alternativos...\n")
  
  simbolos_alternativos <- c("EUR=X", "EURUSD", "EURGBP=X", "GBPUSD=X")
  
  for (simbolo in simbolos_alternativos) {
    cat("Probando", simbolo, "... ")
    
    resultado <- tryCatch({
      datos_test <- getSymbols(simbolo, 
                              src = "yahoo",
                              from = Sys.Date() - 10,
                              to = Sys.Date(),
                              auto.assign = FALSE,
                              warnings = FALSE)
      
      if (!is.null(datos_test) && nrow(datos_test) > 0) {
        cat("✅ FUNCIONA\n")
        TRUE
      } else {
        cat("❌ Sin datos\n")
        FALSE
      }
    }, error = function(e) {
      cat("❌ Error\n")
      FALSE
    })
  }
}

# Ejecutar prueba de símbolos alternativos si la principal falló
if (!resultado_prueba) {
  probar_simbolos_alternativos()
}

cat("\n📝 Nota: Si ningún símbolo funciona, el problema es de conectividad general.\n")
cat("Si algunos símbolos funcionan, puedes modificar el símbolo en la estrategia principal.\n")
