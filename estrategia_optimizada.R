# =============================================================================
# ESTRATEGIA DE TRADING OPTIMIZADA: CRUCE DE MEDIAS MÓVILES + RSI
# =============================================================================
# Versión mejorada con parámetros más realistas y flexibles
# Soluciona el problema de operaciones con rendimiento 0%
# =============================================================================

# Cargar librerías necesarias
library(quantmod)
library(TTR)
library(ggplot2)
library(dplyr)
library(gridExtra)
library(scales)

cat("🚀 ESTRATEGIA DE TRADING OPTIMIZADA\n")
cat("===================================\n\n")

# =============================================================================
# PARÁMETROS OPTIMIZADOS DE LA ESTRATEGIA
# =============================================================================

# Configuración de parámetros (fácilmente modificables)
PARAMETROS <- list(
  # Medias móviles
  ma_corta = 20,        # Cambiado de 50 a 20 (más sensible)
  ma_larga = 50,        # Cambiado de 200 a 50 (más sensible)
  
  # RSI
  rsi_periodo = 14,     # Período del RSI
  rsi_entrada_min = 45, # Cambiado de 50 a 45 (menos restrictivo)
  rsi_salida_max = 75,  # Cambiado de 70 a 75 (menos restrictivo)
  
  # Gestión de riesgo
  stop_loss_pct = 0.015,    # 1.5% (menos restrictivo)
  take_profit_pct = 0.03,   # 3% (más realista)
  
  # Filtros adicionales
  min_dias_posicion = 2,    # Mínimo 2 días en posición
  max_dias_posicion = 15    # Máximo 15 días en posición
)

cat("📋 PARÁMETROS DE LA ESTRATEGIA OPTIMIZADA:\n")
cat("==========================================\n")
cat("Media móvil corta:", PARAMETROS$ma_corta, "días\n")
cat("Media móvil larga:", PARAMETROS$ma_larga, "días\n")
cat("RSI entrada mínima:", PARAMETROS$rsi_entrada_min, "\n")
cat("RSI salida máxima:", PARAMETROS$rsi_salida_max, "\n")
cat("Stop Loss:", PARAMETROS$stop_loss_pct * 100, "%\n")
cat("Take Profit:", PARAMETROS$take_profit_pct * 100, "%\n")
cat("Mínimo días en posición:", PARAMETROS$min_dias_posicion, "\n")
cat("Máximo días en posición:", PARAMETROS$max_dias_posicion, "\n\n")

# =============================================================================
# FUNCIÓN PARA OBTENER DATOS (MEJORADA)
# =============================================================================

obtener_datos_optimizado <- function() {
  cat("📥 Descargando datos de EUR/USD...\n")
  
  fecha_fin <- Sys.Date()
  fecha_inicio <- fecha_fin - 730  # 2 años
  
  tryCatch({
    datos <- getSymbols("EURUSD=X", 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    
    # Limpiar valores faltantes automáticamente
    if (any(is.na(datos))) {
      cat("🧹 Limpiando valores faltantes...\n")
      datos <- na.omit(datos)
    }
    
    cat("✅ Datos obtenidos:", nrow(datos), "días\n")
    return(datos)
    
  }, error = function(e) {
    cat("❌ Error:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# FUNCIÓN PARA CALCULAR INDICADORES (OPTIMIZADA)
# =============================================================================

calcular_indicadores_optimizado <- function(datos) {
  cat("📊 Calculando indicadores técnicos optimizados...\n")
  
  precios <- Cl(datos)
  
  # Calcular medias móviles con parámetros optimizados
  ma_corta <- SMA(precios, n = PARAMETROS$ma_corta)
  ma_larga <- SMA(precios, n = PARAMETROS$ma_larga)
  
  # Calcular RSI
  rsi <- RSI(precios, n = PARAMETROS$rsi_periodo)
  
  # Calcular indicadores adicionales para mejor análisis
  volatilidad <- runSD(precios, n = 20)  # Volatilidad de 20 días
  volumen_promedio <- SMA(Vo(datos), n = 20)  # Volumen promedio
  
  # Crear dataframe completo
  datos_completos <- data.frame(
    Fecha = index(datos),
    Precio = as.numeric(precios),
    MA_Corta = as.numeric(ma_corta),
    MA_Larga = as.numeric(ma_larga),
    RSI = as.numeric(rsi),
    Volatilidad = as.numeric(volatilidad),
    Volumen = as.numeric(Vo(datos)),
    Volumen_Promedio = as.numeric(volumen_promedio),
    High = as.numeric(Hi(datos)),
    Low = as.numeric(Lo(datos)),
    Open = as.numeric(Op(datos))
  )
  
  # Eliminar filas con NAs
  datos_completos <- datos_completos[complete.cases(datos_completos), ]
  
  cat("✅ Indicadores calculados:", nrow(datos_completos), "días válidos\n\n")
  return(datos_completos)
}

# =============================================================================
# ESTRATEGIA OPTIMIZADA CON MEJORES CONDICIONES
# =============================================================================

implementar_estrategia_optimizada <- function(datos) {
  cat("🎯 Implementando estrategia optimizada...\n")
  
  if (is.null(datos) || nrow(datos) == 0) {
    return(data.frame())
  }
  
  # Variables de control
  posicion_abierta <- FALSE
  precio_entrada <- 0
  fecha_entrada <- NULL
  dias_en_posicion <- 0
  stop_loss <- 0
  take_profit <- 0
  operaciones <- data.frame()
  
  # Recorrer datos día por día
  for (i in 2:nrow(datos)) {
    fecha_actual <- datos$Fecha[i]
    precio_actual <- datos$Precio[i]
    ma_corta_actual <- datos$MA_Corta[i]
    ma_larga_actual <- datos$MA_Larga[i]
    ma_corta_anterior <- datos$MA_Corta[i-1]
    ma_larga_anterior <- datos$MA_Larga[i-1]
    rsi_actual <- datos$RSI[i]
    volatilidad_actual <- datos$Volatilidad[i]
    
    # Verificar valores válidos
    if (any(is.na(c(precio_actual, ma_corta_actual, ma_larga_actual, 
                    ma_corta_anterior, ma_larga_anterior, rsi_actual)))) {
      next
    }
    
    # CONDICIONES DE ENTRADA (mejoradas)
    if (!posicion_abierta) {
      # Cruce alcista de medias móviles
      cruce_alcista <- (ma_corta_anterior <= ma_larga_anterior) && 
                       (ma_corta_actual > ma_larga_actual)
      
      # RSI en zona favorable (menos restrictivo)
      rsi_favorable <- rsi_actual >= PARAMETROS$rsi_entrada_min && 
                       rsi_actual <= 65  # No entrar si ya está muy alto
      
      # Filtro de volatilidad (evitar mercados muy volátiles)
      volatilidad_ok <- is.na(volatilidad_actual) || volatilidad_actual < 0.01
      
      # Confirmar tendencia (MA corta debe estar subiendo)
      if (i > 2) {
        ma_corta_subiendo <- datos$MA_Corta[i] > datos$MA_Corta[i-1]
      } else {
        ma_corta_subiendo <- TRUE
      }
      
      # Abrir posición si se cumplen todas las condiciones
      if (cruce_alcista && rsi_favorable && volatilidad_ok && ma_corta_subiendo) {
        posicion_abierta <- TRUE
        precio_entrada <- precio_actual
        fecha_entrada <- fecha_actual
        dias_en_posicion <- 0
        stop_loss <- precio_entrada * (1 - PARAMETROS$stop_loss_pct)
        take_profit <- precio_entrada * (1 + PARAMETROS$take_profit_pct)
        
        cat("📈 ENTRADA:", as.character(fecha_actual), 
            "Precio:", round(precio_entrada, 5), 
            "RSI:", round(rsi_actual, 2), "\n")
      }
    }
    
    # CONDICIONES DE SALIDA (mejoradas)
    if (posicion_abierta) {
      dias_en_posicion <- dias_en_posicion + 1
      salida <- FALSE
      motivo_salida <- ""
      
      # Condición 1: RSI sobrecompra (menos restrictivo)
      if (rsi_actual >= PARAMETROS$rsi_salida_max && dias_en_posicion >= PARAMETROS$min_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "RSI_Sobrecompra"
      }
      
      # Condición 2: Precio por debajo de MA corta (con filtro de tiempo)
      if (precio_actual < ma_corta_actual && dias_en_posicion >= PARAMETROS$min_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "Precio_Bajo_MA"
      }
      
      # Condición 3: Stop Loss
      if (precio_actual <= stop_loss) {
        salida <- TRUE
        motivo_salida <- "Stop_Loss"
      }
      
      # Condición 4: Take Profit
      if (precio_actual >= take_profit) {
        salida <- TRUE
        motivo_salida <- "Take_Profit"
      }
      
      # Condición 5: Máximo tiempo en posición
      if (dias_en_posicion >= PARAMETROS$max_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "Tiempo_Maximo"
      }
      
      # Ejecutar salida
      if (salida) {
        precio_salida <- precio_actual
        rendimiento <- (precio_salida - precio_entrada) / precio_entrada
        rendimiento_pct <- rendimiento * 100
        
        # Registrar operación
        nueva_operacion <- data.frame(
          Entrada_Fecha = fecha_entrada,
          Entrada_Precio = precio_entrada,
          Salida_Fecha = fecha_actual,
          Salida_Precio = precio_salida,
          Dias_Posicion = dias_en_posicion,
          Rendimiento_Pct = rendimiento_pct,
          Motivo_Salida = motivo_salida,
          Ganadora = rendimiento > 0,
          RSI_Entrada = datos$RSI[which(datos$Fecha == fecha_entrada)],
          RSI_Salida = rsi_actual
        )
        
        operaciones <- rbind(operaciones, nueva_operacion)
        
        cat("📉 SALIDA:", as.character(fecha_actual), 
            "Precio:", round(precio_salida, 5), 
            "Días:", dias_en_posicion,
            "Rendimiento:", round(rendimiento_pct, 2), "%",
            "Motivo:", motivo_salida, "\n")
        
        # Reset variables
        posicion_abierta <- FALSE
        precio_entrada <- 0
        fecha_entrada <- NULL
        dias_en_posicion <- 0
      }
    }
  }
  
  cat("✅ Estrategia completada:", nrow(operaciones), "operaciones\n\n")
  return(operaciones)
}

# =============================================================================
# EJECUCIÓN DE LA ESTRATEGIA OPTIMIZADA
# =============================================================================

# Obtener datos
datos_eurusd <- obtener_datos_optimizado()

if (!is.null(datos_eurusd)) {
  # Calcular indicadores
  datos_con_indicadores <- calcular_indicadores_optimizado(datos_eurusd)
  
  # Ejecutar estrategia
  resultados <- implementar_estrategia_optimizada(datos_con_indicadores)
  
  # Mostrar resultados
  if (nrow(resultados) > 0) {
    cat("📊 RESULTADOS DE LA ESTRATEGIA OPTIMIZADA:\n")
    cat("=========================================\n")
    
    total_ops <- nrow(resultados)
    ops_ganadoras <- sum(resultados$Ganadora)
    tasa_exito <- (ops_ganadoras / total_ops) * 100
    rendimiento_total <- sum(resultados$Rendimiento_Pct)
    rendimiento_promedio <- mean(resultados$Rendimiento_Pct)
    dias_promedio <- mean(resultados$Dias_Posicion)
    
    cat("Total operaciones:", total_ops, "\n")
    cat("Operaciones ganadoras:", ops_ganadoras, "\n")
    cat("Tasa de éxito:", round(tasa_exito, 1), "%\n")
    cat("Rendimiento total:", round(rendimiento_total, 2), "%\n")
    cat("Rendimiento promedio:", round(rendimiento_promedio, 2), "%\n")
    cat("Días promedio en posición:", round(dias_promedio, 1), "\n")
    
    cat("\n📋 DETALLE DE OPERACIONES:\n")
    print(resultados[, c("Entrada_Fecha", "Salida_Fecha", "Dias_Posicion", 
                         "Rendimiento_Pct", "Motivo_Salida", "Ganadora")])
    
  } else {
    cat("❌ No se generaron operaciones con los parámetros optimizados\n")
    cat("💡 Sugerencias:\n")
    cat("- Reducir el período de MA larga (actualmente", PARAMETROS$ma_larga, ")\n")
    cat("- Reducir RSI mínimo de entrada (actualmente", PARAMETROS$rsi_entrada_min, ")\n")
    cat("- Aumentar el período de análisis\n")
  }
  
} else {
  cat("❌ No se pudieron obtener datos\n")
}

cat("\n🎯 PRÓXIMOS PASOS:\n")
cat("1. Analizar los resultados obtenidos\n")
cat("2. Ajustar parámetros según sea necesario\n")
cat("3. Probar con diferentes configuraciones\n")
cat("4. Implementar filtros adicionales\n")

