# =============================================================================
# DIAGNÓSTICO Y LIMPIEZA DE DATOS FALTANTES
# =============================================================================
# Este script diagnostica y maneja valores faltantes (NA) en datos de EUR/USD
# Útil cuando aparece el mensaje de "valores faltantes" al descargar datos
# =============================================================================

library(quantmod)
library(zoo)  # Para funciones de interpolación
library(ggplot2)
library(dplyr)

cat("🔍 DIAGNÓSTICO DE DATOS FALTANTES EN EUR/USD\n")
cat("===========================================\n\n")

# =============================================================================
# FUNCIÓN PARA DESCARGAR Y ANALIZAR DATOS
# =============================================================================

analizar_datos_faltantes <- function(simbolo = "EURUSD=X", dias = 730) {
  cat("📥 Descargando datos de", simbolo, "...\n")
  
  # Calcular fechas
  fecha_fin <- Sys.Date()
  fecha_inicio <- fecha_fin - dias
  
  # Descargar datos
  tryCatch({
    datos <- getSymbols(simbolo, 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) == 0) {
      cat("❌ No se obtuvieron datos\n")
      return(NULL)
    }
    
    # Renombrar columnas
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    
    cat("✅ Datos descargados:", nrow(datos), "filas\n")
    cat("Período:", as.character(fecha_inicio), "a", as.character(fecha_fin), "\n\n")
    
    return(datos)
    
  }, error = function(e) {
    cat("❌ Error descargando datos:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# FUNCIÓN PARA DIAGNOSTICAR VALORES FALTANTES
# =============================================================================

diagnosticar_nas <- function(datos) {
  if (is.null(datos)) {
    cat("❌ No hay datos para diagnosticar\n")
    return()
  }
  
  cat("🔍 DIAGNÓSTICO DE VALORES FALTANTES\n")
  cat("===================================\n")
  
  # Contar NAs por columna
  nas_por_columna <- sapply(datos, function(x) sum(is.na(x)))
  total_nas <- sum(nas_por_columna)
  total_valores <- nrow(datos) * ncol(datos)
  porcentaje_nas <- (total_nas / total_valores) * 100
  
  cat("Total de valores:", total_valores, "\n")
  cat("Total de NAs:", total_nas, "\n")
  cat("Porcentaje de NAs:", round(porcentaje_nas, 2), "%\n\n")
  
  if (total_nas == 0) {
    cat("✅ ¡Excelente! No hay valores faltantes\n")
    return(list(tiene_nas = FALSE, datos_limpios = datos))
  }
  
  cat("📊 DESGLOSE POR COLUMNA:\n")
  for (col in names(nas_por_columna)) {
    nas_col <- nas_por_columna[col]
    porcentaje_col <- (nas_col / nrow(datos)) * 100
    
    if (nas_col > 0) {
      cat("❌", col, ":", nas_col, "NAs (", round(porcentaje_col, 2), "%)\n")
    } else {
      cat("✅", col, ": Sin NAs\n")
    }
  }
  
  # Identificar patrones de NAs
  cat("\n🔍 ANÁLISIS DE PATRONES:\n")
  
  # Encontrar filas con NAs
  filas_con_nas <- which(apply(is.na(datos), 1, any))
  
  if (length(filas_con_nas) > 0) {
    cat("Filas con NAs:", length(filas_con_nas), "\n")
    cat("Primera fila con NA:", filas_con_nas[1], "\n")
    cat("Última fila con NA:", filas_con_nas[length(filas_con_nas)], "\n")
    
    # Verificar si los NAs están concentrados
    if (length(filas_con_nas) > 1) {
      diferencias <- diff(filas_con_nas)
      nas_consecutivos <- any(diferencias == 1)
      
      if (nas_consecutivos) {
        cat("⚠️ Se detectaron NAs consecutivos\n")
      } else {
        cat("ℹ️ Los NAs están dispersos\n")
      }
    }
    
    # Mostrar fechas con problemas
    fechas_problematicas <- index(datos)[filas_con_nas]
    cat("\n📅 FECHAS CON VALORES FALTANTES (primeras 10):\n")
    print(head(fechas_problematicas, 10))
    
    if (length(fechas_problematicas) > 10) {
      cat("... y", length(fechas_problematicas) - 10, "más\n")
    }
  }
  
  return(list(
    tiene_nas = TRUE,
    total_nas = total_nas,
    nas_por_columna = nas_por_columna,
    filas_con_nas = filas_con_nas,
    porcentaje_nas = porcentaje_nas
  ))
}

# =============================================================================
# FUNCIONES DE LIMPIEZA
# =============================================================================

limpiar_datos_metodo1 <- function(datos) {
  cat("\n🧹 MÉTODO 1: Eliminar filas con NAs\n")
  cat("===================================\n")
  
  datos_originales <- nrow(datos)
  datos_limpios <- na.omit(datos)
  filas_eliminadas <- datos_originales - nrow(datos_limpios)
  
  cat("Filas originales:", datos_originales, "\n")
  cat("Filas eliminadas:", filas_eliminadas, "\n")
  cat("Filas restantes:", nrow(datos_limpios), "\n")
  cat("Porcentaje conservado:", round((nrow(datos_limpios)/datos_originales)*100, 2), "%\n")
  
  if (nrow(datos_limpios) < 200) {
    cat("⚠️ ADVERTENCIA: Muy pocos datos restantes para análisis\n")
  } else {
    cat("✅ Suficientes datos para análisis\n")
  }
  
  return(datos_limpios)
}

limpiar_datos_metodo2 <- function(datos) {
  cat("\n🔄 MÉTODO 2: Interpolación de valores faltantes\n")
  cat("===============================================\n")
  
  tryCatch({
    # Usar interpolación lineal para llenar NAs
    datos_interpolados <- na.approx(datos, na.rm = FALSE)
    
    # Eliminar NAs restantes (al inicio o final de la serie)
    datos_limpios <- na.omit(datos_interpolados)
    
    cat("✅ Interpolación exitosa\n")
    cat("Filas después de interpolación:", nrow(datos_limpios), "\n")
    
    return(datos_limpios)
    
  }, error = function(e) {
    cat("❌ Error en interpolación:", e$message, "\n")
    cat("Usando método de eliminación como respaldo...\n")
    return(limpiar_datos_metodo1(datos))
  })
}

limpiar_datos_metodo3 <- function(datos) {
  cat("\n📊 MÉTODO 3: Llenar con último valor válido\n")
  cat("==========================================\n")
  
  tryCatch({
    # Usar na.locf (Last Observation Carried Forward)
    datos_llenados <- na.locf(datos, na.rm = FALSE)
    
    # Eliminar NAs restantes
    datos_limpios <- na.omit(datos_llenados)
    
    cat("✅ Llenado exitoso\n")
    cat("Filas después de llenado:", nrow(datos_limpios), "\n")
    
    return(datos_limpios)
    
  }, error = function(e) {
    cat("❌ Error en llenado:", e$message, "\n")
    return(limpiar_datos_metodo1(datos))
  })
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE LIMPIEZA AUTOMÁTICA
# =============================================================================

limpiar_datos_automatico <- function(datos) {
  cat("\n🤖 LIMPIEZA AUTOMÁTICA DE DATOS\n")
  cat("===============================\n")
  
  diagnostico <- diagnosticar_nas(datos)
  
  if (!diagnostico$tiene_nas) {
    cat("✅ No se requiere limpieza\n")
    return(datos)
  }
  
  # Decidir método basado en el porcentaje de NAs
  if (diagnostico$porcentaje_nas < 5) {
    cat("📝 Pocos NAs detectados (< 5%), usando eliminación simple\n")
    return(limpiar_datos_metodo1(datos))
    
  } else if (diagnostico$porcentaje_nas < 15) {
    cat("📝 NAs moderados detectados (5-15%), usando interpolación\n")
    return(limpiar_datos_metodo2(datos))
    
  } else {
    cat("📝 Muchos NAs detectados (> 15%), usando último valor válido\n")
    return(limpiar_datos_metodo3(datos))
  }
}

# =============================================================================
# EJECUCIÓN PRINCIPAL
# =============================================================================

# Descargar datos
datos_eurusd <- analizar_datos_faltantes("EURUSD=X", 730)

if (!is.null(datos_eurusd)) {
  # Diagnosticar problemas
  diagnostico <- diagnosticar_nas(datos_eurusd)
  
  if (diagnostico$tiene_nas) {
    cat("\n🛠️ OPCIONES DE LIMPIEZA DISPONIBLES:\n")
    cat("====================================\n")
    cat("1. Eliminación simple (conservador)\n")
    cat("2. Interpolación lineal (moderado)\n")
    cat("3. Último valor válido (agresivo)\n")
    cat("4. Automático (recomendado)\n\n")
    
    # Aplicar limpieza automática
    datos_limpios <- limpiar_datos_automatico(datos_eurusd)
    
    cat("\n✅ DATOS LISTOS PARA ANÁLISIS\n")
    cat("=============================\n")
    cat("Filas finales:", nrow(datos_limpios), "\n")
    cat("Período:", as.character(index(datos_limpios)[1]), "a", 
        as.character(index(datos_limpios)[nrow(datos_limpios)]), "\n")
    
    # Verificación final
    nas_finales <- sum(is.na(datos_limpios))
    if (nas_finales == 0) {
      cat("✅ Sin valores faltantes restantes\n")
      cat("🎯 Los datos están listos para la estrategia de trading\n")
    } else {
      cat("⚠️ Aún quedan", nas_finales, "valores faltantes\n")
    }
    
  } else {
    cat("\n🎉 ¡PERFECTO! Los datos no tienen valores faltantes\n")
    cat("Puedes proceder directamente con la estrategia de trading\n")
  }
  
} else {
  cat("\n❌ No se pudieron obtener datos para análisis\n")
  cat("Verifica tu conexión a internet e intenta nuevamente\n")
}

cat("\n", paste(rep("=", 50), collapse = ""), "\n")
cat("💡 RECOMENDACIÓN:\n")
cat("Si este script resuelve los problemas de NAs,\n")
cat("la estrategia principal debería funcionar correctamente.\n")
cat(paste(rep("=", 50), collapse = ""), "\n")
