# =============================================================================
# FUTUROS HOY - ¿PUEDO OPERAR AHORA?
# =============================================================================
# Análisis específico para saber si hay oportunidades HOY
# Respuesta directa: SÍ/NO puedes operar ahora
# =============================================================================

library(quantmod)
library(TTR)

cat("📈 FUTUROS HOY - ANÁLISIS INMEDIATO\n")
cat("===================================\n")
cat("Fecha HOY:", format(Sys.Date(), "%Y-%m-%d"), "\n")
cat("Hora actual:", format(Sys.time(), "%H:%M:%S"), "\n\n")

# =============================================================================
# CONFIGURACIÓN PARA HOY
# =============================================================================

CONFIG_HOY <- list(
  capital_disponible = 25000,
  riesgo_por_operacion = 500,  # €500 por operación
  
  # Mercados principales para futuros
  mercados = list(
    "ORO" = list(
      simbolo = "GC=F",
      nombre = "Oro Futuros",
      valor_punto = 100,
      margen_requerido = 8000,
      tick_minimo = 0.10,
      horario_activo = c(1, 23)  # 24h casi
    ),
    
    "SP500" = list(
      simbolo = "ES=F", 
      nombre = "S&P 500 E-mini",
      valor_punto = 50,
      margen_requerido = 13000,
      tick_minimo = 0.25,
      horario_activo = c(9, 16)  # 9:30-16:00 EST
    ),
    
    "NASDAQ" = list(
      simbolo = "NQ=F",
      nombre = "Nasdaq 100 E-mini", 
      valor_punto = 20,
      margen_requerido = 17000,
      tick_minimo = 0.25,
      horario_activo = c(9, 16)
    ),
    
    "PETROLEO" = list(
      simbolo = "CL=F",
      nombre = "Petróleo Crudo",
      valor_punto = 1000,
      margen_requerido = 6000,
      tick_minimo = 0.01,
      horario_activo = c(9, 14)  # 9:00-14:30 EST
    )
  )
)

# =============================================================================
# FUNCIÓN PARA VERIFICAR SI PUEDO OPERAR HOY
# =============================================================================

puedo_operar_hoy <- function(mercado_info, nombre_mercado) {
  cat("🔍 Analizando", mercado_info$nombre, "para HOY...")
  
  tryCatch({
    # Obtener datos muy recientes
    fecha_fin <- Sys.Date()
    fecha_inicio <- fecha_fin - 30  # 1 mes
    
    datos <- getSymbols(mercado_info$simbolo,
                       src = "yahoo", 
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) < 10) {
      cat(" ❌ Sin datos\n")
      return(NULL)
    }
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos <- na.omit(datos)
    
    # Calcular indicadores simples
    precios <- Cl(datos)
    ma_corta <- SMA(precios, n = 5)   # MA muy rápida
    ma_media <- SMA(precios, n = 10)  # MA rápida
    ma_larga <- SMA(precios, n = 20)  # MA media
    rsi <- RSI(precios, n = 10)       # RSI sensible
    
    # Datos de HOY (último disponible)
    n <- nrow(datos)
    fecha_ultimo <- as.Date(index(datos)[n])
    precio_actual <- as.numeric(precios[n])
    ma_corta_actual <- as.numeric(ma_corta[n])
    ma_media_actual <- as.numeric(ma_media[n])
    ma_larga_actual <- as.numeric(ma_larga[n])
    rsi_actual <- as.numeric(rsi[n])
    
    # Verificar si los datos son de hoy o muy recientes
    dias_antiguedad <- as.numeric(Sys.Date() - fecha_ultimo)
    
    if (dias_antiguedad > 3) {
      cat(" ⚠️ Datos antiguos (", dias_antiguedad, "días)\n")
      return(NULL)
    }
    
    # Evaluar señal para HOY
    señal_hoy <- evaluar_señal_hoy(precio_actual, ma_corta_actual, ma_media_actual, 
                                  ma_larga_actual, rsi_actual, mercado_info)
    
    cat(" ✅", señal_hoy$decision, "\n")
    
    return(list(
      mercado = nombre_mercado,
      nombre = mercado_info$nombre,
      precio = precio_actual,
      fecha_datos = fecha_ultimo,
      dias_antiguedad = dias_antiguedad,
      ma_corta = ma_corta_actual,
      ma_media = ma_media_actual,
      ma_larga = ma_larga_actual,
      rsi = rsi_actual,
      señal = señal_hoy,
      info = mercado_info
    ))
    
  }, error = function(e) {
    cat(" ❌ Error\n")
    return(NULL)
  })
}

# =============================================================================
# FUNCIÓN PARA EVALUAR SEÑAL DE HOY
# =============================================================================

evaluar_señal_hoy <- function(precio, ma_corta, ma_media, ma_larga, rsi, mercado_info) {
  
  # Verificar datos válidos
  if (any(is.na(c(precio, ma_corta, ma_media, ma_larga, rsi)))) {
    return(list(
      decision = "NO_OPERAR",
      razon = "Datos insuficientes",
      accion = "Esperar datos completos",
      confianza = 0
    ))
  }
  
  # Verificar horario de trading
  hora_actual <- as.numeric(format(Sys.time(), "%H"))
  if (mercado_info$nombre != "Oro Futuros") {  # Oro es 24h
    if (hora_actual < mercado_info$horario_activo[1] || 
        hora_actual > mercado_info$horario_activo[2]) {
      return(list(
        decision = "NO_OPERAR",
        razon = "Fuera de horario",
        accion = paste("Esperar hasta", mercado_info$horario_activo[1], ":00"),
        confianza = 0
      ))
    }
  }
  
  # Evaluar condiciones técnicas
  
  # 1. Tendencia alcista fuerte
  if (ma_corta > ma_media && ma_media > ma_larga && precio > ma_corta && rsi > 50 && rsi < 80) {
    return(list(
      decision = "COMPRAR_HOY",
      razon = "Tendencia alcista fuerte",
      accion = "Ejecutar compra con stop ajustado",
      confianza = 85
    ))
  }
  
  # 2. Breakout alcista
  if (precio > ma_larga && ma_corta > ma_media && rsi > 55 && rsi < 75) {
    return(list(
      decision = "COMPRAR_HOY", 
      razon = "Breakout alcista",
      accion = "Comprar en pullback o breakout",
      confianza = 75
    ))
  }
  
  # 3. Reversión desde sobreventa
  if (rsi < 30 && precio < ma_media && ma_corta < ma_media) {
    return(list(
      decision = "VIGILAR",
      razon = "Posible reversión",
      accion = "Esperar confirmación de reversión",
      confianza = 60
    ))
  }
  
  # 4. Tendencia bajista
  if (ma_corta < ma_media && ma_media < ma_larga && precio < ma_corta) {
    return(list(
      decision = "NO_OPERAR",
      razon = "Tendencia bajista",
      accion = "Esperar cambio de tendencia",
      confianza = 20
    ))
  }
  
  # 5. Mercado lateral
  return(list(
    decision = "ESPERAR",
    razon = "Mercado sin dirección clara",
    accion = "Esperar breakout o reversión",
    confianza = 40
  ))
}

# =============================================================================
# FUNCIÓN PARA CALCULAR NIVELES DE HOY
# =============================================================================

calcular_niveles_hoy <- function(resultado) {
  precio <- resultado$precio
  mercado_info <- resultado$info
  
  # Stop loss y take profit conservadores para day trading
  if (mercado_info$nombre == "Oro Futuros") {
    stop_loss <- precio - 20  # $20 por onza
    take_profit <- precio + 40  # $40 por onza
  } else if (grepl("S&P", mercado_info$nombre)) {
    stop_loss <- precio - 10  # 10 puntos
    take_profit <- precio + 20  # 20 puntos
  } else if (grepl("Nasdaq", mercado_info$nombre)) {
    stop_loss <- precio - 30  # 30 puntos
    take_profit <- precio + 60  # 60 puntos
  } else if (grepl("Petróleo", mercado_info$nombre)) {
    stop_loss <- precio - 1.0  # $1 por barril
    take_profit <- precio + 2.0  # $2 por barril
  } else {
    # Genérico: 1.5% stop, 3% target
    stop_loss <- precio * 0.985
    take_profit <- precio * 1.03
  }
  
  # Calcular contratos basado en riesgo
  riesgo_puntos <- precio - stop_loss
  valor_por_punto <- mercado_info$valor_punto
  riesgo_euros_objetivo <- CONFIG_HOY$riesgo_por_operacion
  
  if (riesgo_puntos > 0) {
    num_contratos <- floor(riesgo_euros_objetivo / (riesgo_puntos * valor_por_punto))
    num_contratos <- max(1, num_contratos)
  } else {
    num_contratos <- 1
  }
  
  # Verificar margen disponible
  margen_total <- num_contratos * mercado_info$margen_requerido
  if (margen_total > CONFIG_HOY$capital_disponible * 0.8) {
    num_contratos <- floor((CONFIG_HOY$capital_disponible * 0.8) / mercado_info$margen_requerido)
    num_contratos <- max(1, num_contratos)
    margen_total <- num_contratos * mercado_info$margen_requerido
  }
  
  riesgo_real <- num_contratos * riesgo_puntos * valor_por_punto
  ganancia_potencial <- num_contratos * (take_profit - precio) * valor_por_punto
  
  return(list(
    precio_entrada = precio,
    stop_loss = stop_loss,
    take_profit = take_profit,
    num_contratos = num_contratos,
    margen_requerido = margen_total,
    riesgo_euros = riesgo_real,
    ganancia_potencial = ganancia_potencial
  ))
}

# =============================================================================
# FUNCIÓN PRINCIPAL
# =============================================================================

analizar_futuros_hoy <- function() {
  cat("🎯 ¿PUEDO OPERAR FUTUROS HOY?\n")
  cat("=============================\n\n")
  
  resultados <- list()
  
  # Analizar cada mercado
  for (nombre_mercado in names(CONFIG_HOY$mercados)) {
    mercado_info <- CONFIG_HOY$mercados[[nombre_mercado]]
    resultado <- puedo_operar_hoy(mercado_info, nombre_mercado)
    
    if (!is.null(resultado)) {
      resultados[[length(resultados) + 1]] <- resultado
    }
    
    Sys.sleep(0.3)
  }
  
  # Mostrar decisiones para HOY
  mostrar_decisiones_hoy(resultados)
}

# =============================================================================
# FUNCIÓN PARA MOSTRAR DECISIONES DE HOY
# =============================================================================

mostrar_decisiones_hoy <- function(resultados) {
  if (length(resultados) == 0) {
    cat("❌ No se pudieron obtener datos actualizados\n")
    cat("💡 Intentar más tarde o verificar conexión\n")
    return()
  }
  
  cat("\n📊 DECISIONES PARA HOY", format(Sys.Date(), "%Y-%m-%d"), "\n")
  cat("==============================================\n")
  
  # Separar por decisión
  comprar_hoy <- Filter(function(r) r$señal$decision == "COMPRAR_HOY", resultados)
  vigilar <- Filter(function(r) r$señal$decision == "VIGILAR", resultados)
  esperar <- Filter(function(r) r$señal$decision == "ESPERAR", resultados)
  no_operar <- Filter(function(r) r$señal$decision == "NO_OPERAR", resultados)
  
  cat("🚀 COMPRAR HOY:", length(comprar_hoy), "\n")
  cat("👀 VIGILAR:", length(vigilar), "\n") 
  cat("⏸️ ESPERAR:", length(esperar), "\n")
  cat("❌ NO OPERAR:", length(no_operar), "\n\n")
  
  # Mostrar oportunidades de compra
  if (length(comprar_hoy) > 0) {
    cat("🎉 ¡OPORTUNIDADES PARA HOY!\n")
    cat("===========================\n")
    
    for (resultado in comprar_hoy) {
      niveles <- calcular_niveles_hoy(resultado)
      
      cat("📈", resultado$nombre, "- ¡COMPRAR HOY!\n")
      cat("   Precio actual:", round(resultado$precio, 2), "\n")
      cat("   RSI:", round(resultado$rsi, 2), "\n")
      cat("   Razón:", resultado$señal$razon, "\n")
      cat("   Confianza:", resultado$señal$confianza, "%\n")
      cat("   Datos de:", as.character(resultado$fecha_datos), 
          "(", resultado$dias_antiguedad, "días)\n")
      
      cat("   💰 NIVELES PARA OPERAR HOY:\n")
      cat("      Contratos:", niveles$num_contratos, "\n")
      cat("      Precio entrada:", round(niveles$precio_entrada, 2), "\n")
      cat("      Stop Loss:", round(niveles$stop_loss, 2), "\n")
      cat("      Take Profit:", round(niveles$take_profit, 2), "\n")
      cat("      Margen requerido:", round(niveles$margen_requerido, 0), "€\n")
      cat("      Riesgo:", round(niveles$riesgo_euros, 2), "€\n")
      cat("      Ganancia potencial:", round(niveles$ganancia_potencial, 2), "€\n\n")
    }
    
    cat("📋 PASOS PARA OPERAR HOY:\n")
    cat("1. Verificar margen disponible en tu cuenta\n")
    cat("2. Abrir plataforma de futuros\n")
    cat("3. Buscar el contrato recomendado\n")
    cat("4. Ejecutar orden con niveles calculados\n")
    cat("5. Colocar stop loss inmediatamente\n")
    cat("6. Monitorear durante el día\n")
    
  } else {
    cat("⏸️ NO HAY OPORTUNIDADES CLARAS HOY\n")
    cat("===================================\n")
    
    if (length(vigilar) > 0) {
      cat("👀 MERCADOS A VIGILAR:\n")
      for (resultado in vigilar) {
        cat("•", resultado$nombre, ":", resultado$señal$razon, "\n")
        cat("  Acción:", resultado$señal$accion, "\n")
      }
    }
    
    cat("\n💡 RECOMENDACIONES:\n")
    cat("- Revisar en 2-4 horas\n")
    cat("- Vigilar noticias económicas\n")
    cat("- Preparar para mañana\n")
    cat("- No forzar operaciones\n")
  }
  
  cat("\n⏰ Próxima revisión recomendada: En 2-4 horas\n")
  cat("📱 Hora actual:", format(Sys.time(), "%H:%M"), "\n")
}

# =============================================================================
# EJECUCIÓN
# =============================================================================

analizar_futuros_hoy()

cat("\n📈 ANÁLISIS DE FUTUROS HOY COMPLETADO\n")
cat("====================================\n")
cat("💡 Este análisis es específico para HOY\n")
cat("🔄 Ejecutar 2-3 veces por día\n")
cat("⚠️ Verificar siempre márgenes antes de operar\n")
