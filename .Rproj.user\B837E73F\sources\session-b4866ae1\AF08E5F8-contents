# =============================================================================
# ESTRATEGIA DE TENDENCIA EXTENDIDA
# =============================================================================
# Basada en el descubrimiento: La única operación ganadora duró 15 días
# Permite que las tendencias se desarrollen completamente
# =============================================================================

library(quantmod)
library(TTR)
library(ggplot2)
library(dplyr)

cat("📈 ESTRATEGIA DE TENDENCIA EXTENDIDA\n")
cat("====================================\n\n")

# =============================================================================
# PARÁMETROS OPTIMIZADOS PARA TENDENCIAS LARGAS
# =============================================================================

PARAMETROS_TENDENCIA <- list(
  # Medias móviles (mantener lo que funciona)
  ma_corta = 20,
  ma_larga = 50,
  
  # RSI (mantener rango efectivo)
  rsi_periodo = 14,
  rsi_entrada_min = 45,
  rsi_entrada_max = 65,
  rsi_salida_max = 75,
  
  # Gestión de riesgo AJUSTADA para tendencias largas
  stop_loss_pct = 0.02,     # 2% (un poco más amplio)
  take_profit_pct = 0.04,   # 4% (más ambicioso)
  
  # TIEMPO EXTENDIDO (clave del descubrimiento)
  min_dias_posicion = 5,    # Mínimo 5 días (era 2)
  max_dias_posicion = 25,   # Máximo 25 días (era 15)
  
  # Filtros de salida MODIFICADOS
  usar_confirmacion_salida = TRUE,  # Confirmar antes de salir
  dias_confirmacion = 2,            # Confirmar ruptura por 2 días
  
  # Filtro de volatilidad (mantener)
  usar_filtro_volatilidad = TRUE,
  volatilidad_max = 0.008
)

cat("📋 PARÁMETROS PARA TENDENCIAS EXTENDIDAS:\n")
cat("=========================================\n")
for (param in names(PARAMETROS_TENDENCIA)) {
  valor <- PARAMETROS_TENDENCIA[[param]]
  if (is.numeric(valor) && valor < 1 && valor > 0) {
    cat(param, ":", valor * 100, "%\n")
  } else {
    cat(param, ":", valor, "\n")
  }
}
cat("\n")

# =============================================================================
# OBTENER DATOS (MISMO MÉTODO CONFIABLE)
# =============================================================================

obtener_datos_tendencia <- function() {
  cat("📥 Descargando datos EUR/USD (3 años)...\n")
  
  fecha_fin <- Sys.Date()
  fecha_inicio <- fecha_fin - 1095
  
  tryCatch({
    datos <- getSymbols("EURUSD=X", 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos <- na.omit(datos)
    
    cat("✅ Datos obtenidos:", nrow(datos), "días\n")
    cat("Período:", as.character(fecha_inicio), "a", as.character(fecha_fin), "\n\n")
    
    return(datos)
    
  }, error = function(e) {
    cat("❌ Error:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# INDICADORES CON SEGUIMIENTO DE TENDENCIA
# =============================================================================

calcular_indicadores_tendencia <- function(datos) {
  cat("📊 Calculando indicadores para seguimiento de tendencia...\n")
  
  precios <- Cl(datos)
  
  # Indicadores principales
  ma_corta <- SMA(precios, n = PARAMETROS_TENDENCIA$ma_corta)
  ma_larga <- SMA(precios, n = PARAMETROS_TENDENCIA$ma_larga)
  rsi <- RSI(precios, n = PARAMETROS_TENDENCIA$rsi_periodo)
  volatilidad <- runSD(precios, n = 20)
  
  # Indicadores adicionales para seguimiento de tendencia
  ma_pendiente <- diff(ma_corta, lag = 3)  # Pendiente de MA corta
  
  # Dataframe completo
  datos_completos <- data.frame(
    Fecha = index(datos),
    Precio = as.numeric(precios),
    MA_Corta = as.numeric(ma_corta),
    MA_Larga = as.numeric(ma_larga),
    RSI = as.numeric(rsi),
    Volatilidad = as.numeric(volatilidad),
    MA_Pendiente = c(rep(NA, 3), as.numeric(ma_pendiente)),
    High = as.numeric(Hi(datos)),
    Low = as.numeric(Lo(datos))
  )
  
  datos_completos <- datos_completos[complete.cases(datos_completos), ]
  
  cat("✅ Indicadores calculados:", nrow(datos_completos), "días válidos\n\n")
  return(datos_completos)
}

# =============================================================================
# ESTRATEGIA CON SEGUIMIENTO EXTENDIDO DE TENDENCIA
# =============================================================================

ejecutar_estrategia_tendencia <- function(datos) {
  cat("🚀 Ejecutando estrategia de tendencia extendida...\n")
  
  if (is.null(datos) || nrow(datos) == 0) {
    return(data.frame())
  }
  
  # Variables de control
  posicion_abierta <- FALSE
  precio_entrada <- 0
  fecha_entrada <- NULL
  dias_en_posicion <- 0
  stop_loss <- 0
  take_profit <- 0
  operaciones <- data.frame()
  
  # Variables para confirmación de salida
  dias_bajo_ma <- 0  # Contador de días consecutivos bajo MA
  
  for (i in 2:nrow(datos)) {
    fecha_actual <- datos$Fecha[i]
    precio_actual <- datos$Precio[i]
    ma_corta_actual <- datos$MA_Corta[i]
    ma_larga_actual <- datos$MA_Larga[i]
    ma_corta_anterior <- datos$MA_Corta[i-1]
    ma_larga_anterior <- datos$MA_Larga[i-1]
    rsi_actual <- datos$RSI[i]
    volatilidad_actual <- datos$Volatilidad[i]
    ma_pendiente_actual <- datos$MA_Pendiente[i]
    
    # Verificar datos válidos
    if (any(is.na(c(precio_actual, ma_corta_actual, ma_larga_actual, 
                    ma_corta_anterior, ma_larga_anterior, rsi_actual)))) {
      next
    }
    
    # ENTRADA - MISMAS CONDICIONES PROBADAS
    if (!posicion_abierta) {
      # Condición 1: Cruce alcista
      cruce_alcista <- (ma_corta_anterior <= ma_larga_anterior) && 
                       (ma_corta_actual > ma_larga_actual)
      
      # Condición 2: RSI favorable
      rsi_favorable <- rsi_actual >= PARAMETROS_TENDENCIA$rsi_entrada_min && 
                       rsi_actual <= PARAMETROS_TENDENCIA$rsi_entrada_max
      
      # Condición 3: Volatilidad normal
      volatilidad_ok <- TRUE
      if (PARAMETROS_TENDENCIA$usar_filtro_volatilidad && !is.na(volatilidad_actual)) {
        volatilidad_ok <- volatilidad_actual <= PARAMETROS_TENDENCIA$volatilidad_max
      }
      
      # Condición 4: MA corta con pendiente positiva (nueva)
      pendiente_positiva <- is.na(ma_pendiente_actual) || ma_pendiente_actual > 0
      
      # ENTRADA
      if (cruce_alcista && rsi_favorable && volatilidad_ok && pendiente_positiva) {
        posicion_abierta <- TRUE
        precio_entrada <- precio_actual
        fecha_entrada <- fecha_actual
        dias_en_posicion <- 0
        stop_loss <- precio_entrada * (1 - PARAMETROS_TENDENCIA$stop_loss_pct)
        take_profit <- precio_entrada * (1 + PARAMETROS_TENDENCIA$take_profit_pct)
        dias_bajo_ma <- 0  # Reset contador
        
        cat("📈 ENTRADA:", as.character(fecha_actual), 
            "Precio:", round(precio_entrada, 5), 
            "RSI:", round(rsi_actual, 2), 
            "Pendiente MA:", round(ma_pendiente_actual, 6), "\n")
      }
    }
    
    # SALIDA - LÓGICA MEJORADA PARA TENDENCIAS LARGAS
    if (posicion_abierta) {
      dias_en_posicion <- dias_en_posicion + 1
      salida <- FALSE
      motivo_salida <- ""
      
      # Actualizar contador de días bajo MA
      if (precio_actual < ma_corta_actual) {
        dias_bajo_ma <- dias_bajo_ma + 1
      } else {
        dias_bajo_ma <- 0  # Reset si vuelve arriba
      }
      
      # Condición 1: RSI sobrecompra (solo después del tiempo mínimo)
      if (rsi_actual >= PARAMETROS_TENDENCIA$rsi_salida_max && 
          dias_en_posicion >= PARAMETROS_TENDENCIA$min_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "RSI_Sobrecompra"
      }
      
      # Condición 2: Ruptura CONFIRMADA de tendencia (NUEVA LÓGICA)
      if (PARAMETROS_TENDENCIA$usar_confirmacion_salida) {
        # Solo salir si está bajo MA por varios días consecutivos
        if (dias_bajo_ma >= PARAMETROS_TENDENCIA$dias_confirmacion && 
            dias_en_posicion >= PARAMETROS_TENDENCIA$min_dias_posicion) {
          salida <- TRUE
          motivo_salida <- "Ruptura_Confirmada"
        }
      } else {
        # Lógica original (sin confirmación)
        if (precio_actual < ma_corta_actual && 
            dias_en_posicion >= PARAMETROS_TENDENCIA$min_dias_posicion) {
          salida <- TRUE
          motivo_salida <- "Ruptura_Tendencia"
        }
      }
      
      # Condición 3: Stop Loss
      if (precio_actual <= stop_loss) {
        salida <- TRUE
        motivo_salida <- "Stop_Loss"
      }
      
      # Condición 4: Take Profit
      if (precio_actual >= take_profit) {
        salida <- TRUE
        motivo_salida <- "Take_Profit"
      }
      
      # Condición 5: Tiempo máximo (extendido)
      if (dias_en_posicion >= PARAMETROS_TENDENCIA$max_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "Tiempo_Maximo"
      }
      
      # EJECUTAR SALIDA
      if (salida) {
        precio_salida <- precio_actual
        rendimiento <- (precio_salida - precio_entrada) / precio_entrada
        rendimiento_pct <- rendimiento * 100
        
        # Calcular máxima ganancia durante la operación
        precios_periodo <- datos$Precio[datos$Fecha >= fecha_entrada & datos$Fecha <= fecha_actual]
        max_precio <- max(precios_periodo, na.rm = TRUE)
        max_ganancia_pct <- ((max_precio - precio_entrada) / precio_entrada) * 100
        
        # Registrar operación
        nueva_operacion <- data.frame(
          Entrada_Fecha = fecha_entrada,
          Entrada_Precio = precio_entrada,
          Salida_Fecha = fecha_actual,
          Salida_Precio = precio_salida,
          Dias_Posicion = dias_en_posicion,
          Rendimiento_Pct = rendimiento_pct,
          Max_Ganancia_Pct = max_ganancia_pct,
          Motivo_Salida = motivo_salida,
          Ganadora = rendimiento > 0,
          Dias_Bajo_MA = dias_bajo_ma
        )
        
        operaciones <- rbind(operaciones, nueva_operacion)
        
        cat("📉 SALIDA:", as.character(fecha_actual), 
            "Precio:", round(precio_salida, 5), 
            "Días:", dias_en_posicion,
            "Rendimiento:", round(rendimiento_pct, 2), "%",
            "Max:", round(max_ganancia_pct, 2), "%",
            "Motivo:", motivo_salida, "\n")
        
        # Reset
        posicion_abierta <- FALSE
        precio_entrada <- 0
        fecha_entrada <- NULL
        dias_en_posicion <- 0
        dias_bajo_ma <- 0
      }
    }
  }
  
  cat("✅ Estrategia de tendencia completada:", nrow(operaciones), "operaciones\n\n")
  return(operaciones)
}

# =============================================================================
# ANÁLISIS AVANZADO DE RESULTADOS
# =============================================================================

analizar_resultados_tendencia <- function(resultados) {
  if (nrow(resultados) == 0) {
    cat("❌ No se generaron operaciones\n")
    return()
  }
  
  cat("🎉 RESULTADOS DE ESTRATEGIA DE TENDENCIA EXTENDIDA:\n")
  cat("===================================================\n")
  
  # Métricas principales
  total_ops <- nrow(resultados)
  ops_ganadoras <- sum(resultados$Ganadora)
  tasa_exito <- (ops_ganadoras / total_ops) * 100
  rendimiento_total <- sum(resultados$Rendimiento_Pct)
  rendimiento_promedio <- mean(resultados$Rendimiento_Pct)
  mejor_operacion <- max(resultados$Rendimiento_Pct)
  peor_operacion <- min(resultados$Rendimiento_Pct)
  dias_promedio <- mean(resultados$Dias_Posicion)
  max_ganancia_promedio <- mean(resultados$Max_Ganancia_Pct)
  
  cat("📊 MÉTRICAS PRINCIPALES:\n")
  cat("Total operaciones:", total_ops, "\n")
  cat("Operaciones ganadoras:", ops_ganadoras, "\n")
  cat("Tasa de éxito:", round(tasa_exito, 1), "%\n")
  cat("Rendimiento total:", round(rendimiento_total, 2), "%\n")
  cat("Rendimiento promedio:", round(rendimiento_promedio, 2), "%\n")
  cat("Mejor operación:", round(mejor_operacion, 2), "%\n")
  cat("Peor operación:", round(peor_operacion, 2), "%\n")
  cat("Días promedio en posición:", round(dias_promedio, 1), "\n")
  cat("Máxima ganancia promedio:", round(max_ganancia_promedio, 2), "%\n")
  
  # Análisis de eficiencia (¿cuánto capturamos del potencial?)
  if ("Max_Ganancia_Pct" %in% colnames(resultados)) {
    eficiencia_promedio <- mean(resultados$Rendimiento_Pct / resultados$Max_Ganancia_Pct, na.rm = TRUE) * 100
    cat("Eficiencia de captura:", round(eficiencia_promedio, 1), "%\n")
  }
  
  # Motivos de salida
  cat("\n📋 MOTIVOS DE SALIDA:\n")
  motivos <- table(resultados$Motivo_Salida)
  for (motivo in names(motivos)) {
    cat(motivo, ":", motivos[motivo], "operaciones\n")
  }
  
  # Evaluación mejorada
  cat("\n🎯 EVALUACIÓN COMPARATIVA:\n")
  cat("Versión Balanceada: 5 ops, 20% éxito, -0.36% rendimiento\n")
  cat("Versión Tendencia : ", total_ops, "ops,", round(tasa_exito, 1), "% éxito,", round(rendimiento_total, 2), "% rendimiento\n")
  
  if (tasa_exito > 20) {
    cat("✅ MEJORA en tasa de éxito\n")
  } else {
    cat("⚠️ Tasa de éxito similar\n")
  }
  
  if (rendimiento_total > -0.36) {
    cat("✅ MEJORA en rendimiento\n")
  } else {
    cat("❌ Rendimiento similar o peor\n")
  }
  
  cat("\n📈 DETALLE COMPLETO:\n")
  print(resultados[, c("Entrada_Fecha", "Salida_Fecha", "Dias_Posicion", 
                      "Rendimiento_Pct", "Max_Ganancia_Pct", "Motivo_Salida", "Ganadora")])
}

# =============================================================================
# EJECUCIÓN DE LA ESTRATEGIA DE TENDENCIA EXTENDIDA
# =============================================================================

# Ejecutar estrategia
datos_tendencia <- obtener_datos_tendencia()

if (!is.null(datos_tendencia)) {
  datos_con_indicadores <- calcular_indicadores_tendencia(datos_tendencia)
  resultados_tendencia <- ejecutar_estrategia_tendencia(datos_con_indicadores)
  analizar_resultados_tendencia(resultados_tendencia)
} else {
  cat("❌ No se pudieron obtener datos\n")
}

cat("\n📈 ESTRATEGIA DE TENDENCIA EXTENDIDA COMPLETADA\n")
cat("===============================================\n")
cat("💡 Objetivo: Permitir que las tendencias alcistas se desarrollen completamente\n")
cat("🔑 Clave: Confirmación de ruptura antes de salir prematuramente\n")
