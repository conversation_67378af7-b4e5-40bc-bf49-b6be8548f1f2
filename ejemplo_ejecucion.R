# =============================================================================
# EJEMPLO DE EJECUCIÓN PASO A PASO
# =============================================================================
# Este archivo muestra cómo ejecutar la estrategia de trading paso a paso
# con explicaciones detalladas de cada componente
# =============================================================================

cat("🚀 INICIANDO EJEMPLO DE EJECUCIÓN DE ESTRATEGIA DE TRADING\n")
cat("=========================================================\n\n")

# =============================================================================
# PASO 1: VERIFICAR E INSTALAR LIBRERÍAS
# =============================================================================

cat("📦 PASO 1: Verificando librerías necesarias...\n")

# Lista de librerías requeridas con sus propósitos
librerias_info <- list(
  "quantmod" = "Descarga de datos financieros desde Yahoo Finance",
  "TTR" = "Cálculo de indicadores técnicos (SMA, RSI)",
  "ggplot2" = "Creación de gráficos profesionales",
  "dplyr" = "Manipulación y transformación de datos",
  "gridExtra" = "Organización de múltiples gráficos",
  "scales" = "Formateo de escalas en gráficos"
)

for (lib_name in names(librerias_info)) {
  cat("Verificando", lib_name, "...")
  
  if (!require(lib_name, character.only = TRUE, quietly = TRUE)) {
    cat(" ❌ No encontrada. Instalando...\n")
    install.packages(lib_name, dependencies = TRUE)
    library(lib_name, character.only = TRUE)
    cat("   ✅ Instalada y cargada\n")
  } else {
    cat(" ✅ OK\n")
  }
  
  cat("   Propósito:", librerias_info[[lib_name]], "\n")
}

cat("\n✅ Todas las librerías están listas\n\n")

# =============================================================================
# PASO 2: PROBAR CONEXIÓN DE DATOS
# =============================================================================

cat("🌐 PASO 2: Probando conexión a datos financieros...\n")

# Función simple para probar la descarga
probar_descarga_simple <- function() {
  tryCatch({
    cat("Descargando muestra de EUR/USD (últimos 10 días)...\n")
    
    datos_muestra <- getSymbols("EURUSD=X", 
                               src = "yahoo",
                               from = Sys.Date() - 10,
                               to = Sys.Date(),
                               auto.assign = FALSE,
                               warnings = FALSE)
    
    if (!is.null(datos_muestra) && nrow(datos_muestra) > 0) {
      cat("✅ Conexión exitosa!\n")
      cat("   Datos obtenidos:", nrow(datos_muestra), "días\n")
      cat("   Último precio:", round(as.numeric(Cl(datos_muestra)[nrow(datos_muestra)]), 5), "\n")
      return(TRUE)
    } else {
      cat("❌ No se obtuvieron datos\n")
      return(FALSE)
    }
    
  }, error = function(e) {
    cat("❌ Error:", e$message, "\n")
    return(FALSE)
  })
}

conexion_ok <- probar_descarga_simple()

if (!conexion_ok) {
  cat("\n⚠️ ADVERTENCIA: Problemas de conexión detectados\n")
  cat("Ejecuta 'test_conexion_datos.R' para diagnóstico detallado\n")
  cat("La estrategia principal puede fallar sin conexión a datos\n\n")
} else {
  cat("\n✅ Conexión verificada. Procediendo con la estrategia...\n\n")
}

# =============================================================================
# PASO 3: EXPLICACIÓN DE LA ESTRATEGIA
# =============================================================================

cat("📊 PASO 3: Explicación de la estrategia implementada\n")
cat("====================================================\n")

cat("\n🎯 ESTRATEGIA: Cruce de Medias Móviles + RSI\n")
cat("---------------------------------------------\n")

cat("\n📈 INDICADORES UTILIZADOS:\n")
cat("1. Media Móvil Simple de 50 días (MA50)\n")
cat("   - Representa la tendencia a corto plazo\n")
cat("   - Se calcula promediando los últimos 50 precios de cierre\n\n")

cat("2. Media Móvil Simple de 200 días (MA200)\n")
cat("   - Representa la tendencia a largo plazo\n")
cat("   - Se calcula promediando los últimos 200 precios de cierre\n\n")

cat("3. Índice de Fuerza Relativa (RSI)\n")
cat("   - Oscilador de momentum que va de 0 a 100\n")
cat("   - RSI > 70: Sobrecompra (posible venta)\n")
cat("   - RSI < 30: Sobreventa (posible compra)\n")
cat("   - RSI > 50: Momentum alcista\n\n")

cat("🚪 SEÑALES DE TRADING:\n")
cat("----------------------\n")

cat("📊 ENTRADA (Compra):\n")
cat("- Condición 1: MA50 cruza POR ENCIMA de MA200 (Golden Cross)\n")
cat("- Condición 2: RSI > 50 (confirmación de momentum alcista)\n")
cat("- Ambas condiciones deben cumplirse simultáneamente\n\n")

cat("📉 SALIDA (Venta):\n")
cat("- Opción 1: RSI >= 70 (zona de sobrecompra)\n")
cat("- Opción 2: Precio cae por debajo de MA50\n")
cat("- Opción 3: Stop Loss: -2% desde precio de entrada\n")
cat("- Opción 4: Take Profit: +5% desde precio de entrada\n\n")

cat("⚖️ GESTIÓN DE RIESGO:\n")
cat("- Stop Loss automático: 2% de pérdida máxima por operación\n")
cat("- Take Profit automático: 5% de ganancia objetivo\n")
cat("- Ratio Riesgo/Beneficio: 1:2.5 (arriesgar 2% para ganar 5%)\n\n")

# =============================================================================
# PASO 4: EJECUTAR LA ESTRATEGIA
# =============================================================================

cat("🔄 PASO 4: Ejecutando la estrategia completa...\n")
cat("===============================================\n")

if (conexion_ok) {
  cat("Iniciando análisis completo...\n")
  cat("Esto puede tomar unos momentos...\n\n")
  
  # Ejecutar el script principal
  tryCatch({
    source("estrategia_trading_eurusd.R")
    cat("\n🎉 ¡Estrategia ejecutada exitosamente!\n")
    
  }, error = function(e) {
    cat("\n❌ Error al ejecutar la estrategia:\n")
    cat("Mensaje:", e$message, "\n")
    cat("\nPosibles soluciones:\n")
    cat("1. Verificar que 'estrategia_trading_eurusd.R' esté en el directorio actual\n")
    cat("2. Revisar que todas las librerías estén instaladas correctamente\n")
    cat("3. Verificar conexión a internet\n")
  })
  
} else {
  cat("❌ No se puede ejecutar la estrategia sin conexión a datos\n")
  cat("Por favor, resuelve los problemas de conexión primero\n")
}

# =============================================================================
# PASO 5: INTERPRETACIÓN DE RESULTADOS
# =============================================================================

cat("\n📋 PASO 5: Cómo interpretar los resultados\n")
cat("==========================================\n")

cat("\n📊 GRÁFICOS GENERADOS:\n")
cat("1. Precio y Medias Móviles:\n")
cat("   - Línea negra: Precio de EUR/USD\n")
cat("   - Línea azul: Media móvil de 50 días\n")
cat("   - Línea roja: Media móvil de 200 días\n")
cat("   - Triángulos verdes: Puntos de entrada\n\n")

cat("2. RSI (Índice de Fuerza Relativa):\n")
cat("   - Línea púrpura: Valor del RSI\n")
cat("   - Línea roja (70): Nivel de sobrecompra\n")
cat("   - Línea azul (50): Nivel neutral\n")
cat("   - Línea verde (30): Nivel de sobreventa\n\n")

cat("3. Rendimientos Acumulados:\n")
cat("   - Muestra la evolución de ganancias/pérdidas\n")
cat("   - Línea ascendente: Estrategia rentable\n")
cat("   - Línea descendente: Estrategia con pérdidas\n\n")

cat("4. Distribución de Rendimientos:\n")
cat("   - Verde: Operaciones ganadoras\n")
cat("   - Rojo: Operaciones perdedoras\n")
cat("   - Muestra la frecuencia de cada tipo\n\n")

cat("📈 MÉTRICAS CLAVE:\n")
cat("- Tasa de Éxito: % de operaciones ganadoras\n")
cat("  * >60%: Excelente\n")
cat("  * 50-60%: Aceptable\n")
cat("  * <50%: Necesita mejoras\n\n")

cat("- Rendimiento Total: Ganancia/pérdida acumulada\n")
cat("  * >10%: Muy bueno\n")
cat("  * 0-10%: Positivo pero modesto\n")
cat("  * <0%: Pérdidas (revisar estrategia)\n\n")

cat("- Drawdown Máximo: Mayor pérdida consecutiva\n")
cat("  * <5%: Bajo riesgo\n")
cat("  * 5-10%: Riesgo moderado\n")
cat("  * >10%: Alto riesgo\n\n")

cat("🎯 PRÓXIMOS PASOS:\n")
cat("1. Analizar los resultados obtenidos\n")
cat("2. Identificar fortalezas y debilidades\n")
cat("3. Considerar optimizaciones (cambiar parámetros)\n")
cat("4. Probar en diferentes períodos de tiempo\n")
cat("5. Implementar mejoras basadas en el análisis\n\n")

cat("✅ Ejemplo de ejecución completado\n")
cat("==================================\n")

cat("\n💡 RECORDATORIO IMPORTANTE:\n")
cat("Este es un análisis educativo con datos históricos.\n")
cat("Los resultados pasados NO garantizan rendimientos futuros.\n")
cat("Nunca inviertas dinero real sin entender completamente los riesgos.\n")
