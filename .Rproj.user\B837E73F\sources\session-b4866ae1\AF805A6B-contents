# =============================================================================
# MULTI-CURRENCY SCANNER - BUSCADOR DE OPORTUNIDADES
# =============================================================================
# Analiza múltiples pares de divisas simultáneamente
# Encuentra las mejores oportunidades de trading del día
# =============================================================================

library(quantmod)
library(TTR)

cat("🌍 MULTI-CURRENCY SCANNER\n")
cat("=========================\n")
cat("Buscando oportunidades en múltiples pares de divisas...\n\n")

# =============================================================================
# CONFIGURACIÓN DE PARES Y PARÁMETROS
# =============================================================================

# Pares a analizar (símbolos de Yahoo Finance)
PARES_FOREX <- list(
  "EUR/USD" = "EURUSD=X",
  "GBP/USD" = "GBPUSD=X", 
  "USD/JPY" = "USDJPY=X",
  "AUD/USD" = "AUDUSD=X",
  "USD/CAD" = "USDCAD=X",
  "NZD/USD" = "NZDUSD=X",
  "EUR/GBP" = "EURGBP=X",
  "GBP/JPY" = "GBPJPY=X"
)

# Parámetros de la estrategia (mismos validados)
PARAMETROS <- list(
  ma_corta = 20,
  ma_larga = 50,
  rsi_periodo = 14,
  rsi_entrada_min = 45,
  rsi_entrada_max = 65,
  rsi_salida_max = 75,
  stop_loss_pct = 0.02,
  take_profit_pct = 0.04,
  volatilidad_max = 0.008
)

# =============================================================================
# FUNCIÓN PARA ANALIZAR UN PAR INDIVIDUAL
# =============================================================================

analizar_par <- function(nombre_par, simbolo_yahoo) {
  tryCatch({
    # Descargar datos
    fecha_fin <- Sys.Date()
    fecha_inicio <- fecha_fin - 180  # 6 meses
    
    datos <- getSymbols(simbolo_yahoo, 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) < 100) {
      return(data.frame(
        Par = nombre_par,
        Estado = "Sin datos",
        Precio = NA,
        SMA20 = NA,
        SMA50 = NA,
        RSI = NA,
        Señal = "❌ No disponible",
        Prioridad = 0
      ))
    }
    
    # Limpiar datos
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos <- na.omit(datos)
    
    # Calcular indicadores
    precios <- Cl(datos)
    ma_corta <- SMA(precios, n = PARAMETROS$ma_corta)
    ma_larga <- SMA(precios, n = PARAMETROS$ma_larga)
    rsi <- RSI(precios, n = PARAMETROS$rsi_periodo)
    volatilidad <- runSD(precios, n = 20)
    
    # Crear dataframe
    df <- data.frame(
      Fecha = index(datos),
      Precio = as.numeric(precios),
      MA_Corta = as.numeric(ma_corta),
      MA_Larga = as.numeric(ma_larga),
      RSI = as.numeric(rsi),
      Volatilidad = as.numeric(volatilidad)
    )
    
    df <- df[complete.cases(df), ]
    
    if (nrow(df) < 2) {
      return(data.frame(
        Par = nombre_par,
        Estado = "Datos insuficientes",
        Precio = NA,
        SMA20 = NA,
        SMA50 = NA,
        RSI = NA,
        Señal = "❌ Sin datos",
        Prioridad = 0
      ))
    }
    
    # Datos actuales
    n <- nrow(df)
    hoy <- df[n, ]
    ayer <- df[n-1, ]
    
    # Verificar señales
    cruce_alcista <- (ayer$MA_Corta <= ayer$MA_Larga) && (hoy$MA_Corta > hoy$MA_Larga)
    rsi_favorable <- hoy$RSI >= PARAMETROS$rsi_entrada_min && hoy$RSI <= PARAMETROS$rsi_entrada_max
    volatilidad_ok <- is.na(hoy$Volatilidad) || hoy$Volatilidad <= PARAMETROS$volatilidad_max
    
    # Determinar señal y prioridad
    if (cruce_alcista && rsi_favorable && volatilidad_ok) {
      señal <- "🚀 COMPRAR"
      prioridad <- 5
    } else if (cruce_alcista && rsi_favorable) {
      señal <- "⚠️ COMPRAR (Vol alta)"
      prioridad <- 4
    } else if (cruce_alcista) {
      señal <- "📊 Cruce (RSI malo)"
      prioridad <- 3
    } else if (hoy$MA_Corta > hoy$MA_Larga && rsi_favorable) {
      señal <- "📈 Tendencia alcista"
      prioridad <- 2
    } else {
      señal <- "⏸️ Esperar"
      prioridad <- 1
    }
    
    # Determinar estado de tendencia
    if (hoy$MA_Corta > hoy$MA_Larga) {
      estado <- "Alcista"
    } else {
      estado <- "Bajista"
    }
    
    return(data.frame(
      Par = nombre_par,
      Estado = estado,
      Precio = round(hoy$Precio, 5),
      SMA20 = round(hoy$MA_Corta, 5),
      SMA50 = round(hoy$MA_Larga, 5),
      RSI = round(hoy$RSI, 2),
      Volatilidad = round(hoy$Volatilidad, 4),
      Señal = señal,
      Prioridad = prioridad,
      Cruce = cruce_alcista,
      RSI_OK = rsi_favorable,
      Vol_OK = volatilidad_ok
    ))
    
  }, error = function(e) {
    return(data.frame(
      Par = nombre_par,
      Estado = "Error",
      Precio = NA,
      SMA20 = NA,
      SMA50 = NA,
      RSI = NA,
      Señal = paste("❌ Error:", substr(e$message, 1, 20)),
      Prioridad = 0
    ))
  })
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE ESCANEO
# =============================================================================

escanear_mercados <- function() {
  cat("🔍 Analizando", length(PARES_FOREX), "pares de divisas...\n\n")
  
  resultados <- data.frame()
  
  # Analizar cada par
  for (i in 1:length(PARES_FOREX)) {
    nombre_par <- names(PARES_FOREX)[i]
    simbolo <- PARES_FOREX[[i]]
    
    cat("Analizando", nombre_par, "...")
    
    resultado <- analizar_par(nombre_par, simbolo)
    resultados <- rbind(resultados, resultado)
    
    cat(" ✓\n")
    
    # Pausa pequeña para no sobrecargar Yahoo Finance
    Sys.sleep(0.5)
  }
  
  # Ordenar por prioridad (mejores oportunidades primero)
  resultados <- resultados[order(-resultados$Prioridad), ]
  
  return(resultados)
}

# =============================================================================
# FUNCIÓN PARA MOSTRAR RESULTADOS
# =============================================================================

mostrar_resultados <- function(resultados) {
  cat("\n📊 RESULTADOS DEL ESCANEO\n")
  cat("=========================\n")
  
  # Contar señales por tipo
  señales_compra <- sum(grepl("COMPRAR", resultados$Señal))
  señales_espera <- sum(grepl("Esperar", resultados$Señal))
  señales_tendencia <- sum(grepl("Tendencia", resultados$Señal))
  
  cat("🚀 Señales de COMPRA:", señales_compra, "\n")
  cat("📈 Tendencias alcistas:", señales_tendencia, "\n")
  cat("⏸️ Pares en espera:", señales_espera, "\n\n")
  
  # Mostrar top oportunidades
  if (señales_compra > 0) {
    cat("🏆 MEJORES OPORTUNIDADES:\n")
    cat("=========================\n")
    
    top_oportunidades <- resultados[resultados$Prioridad >= 4, ]
    
    if (nrow(top_oportunidades) > 0) {
      for (i in 1:nrow(top_oportunidades)) {
        par <- top_oportunidades[i, ]
        cat("📈", par$Par, "-", par$Señal, "\n")
        cat("   Precio:", par$Precio, "| RSI:", par$RSI, "| Tendencia:", par$Estado, "\n")
        
        if (par$Prioridad == 5) {
          # Calcular niveles de trading
          precio_entrada <- par$Precio
          stop_loss <- precio_entrada * (1 - PARAMETROS$stop_loss_pct)
          take_profit <- precio_entrada * (1 + PARAMETROS$take_profit_pct)
          
          cat("   💰 Stop Loss:", round(stop_loss, 5), "| Take Profit:", round(take_profit, 5), "\n")
        }
        cat("\n")
      }
    }
  } else {
    cat("⏸️ No hay señales de compra claras hoy\n")
    cat("📊 Mejores pares para vigilar:\n\n")
    
    # Mostrar los 3 mejores aunque no tengan señal perfecta
    top_3 <- head(resultados, 3)
    for (i in 1:nrow(top_3)) {
      par <- top_3[i, ]
      cat(i, ".", par$Par, "-", par$Señal, "| RSI:", par$RSI, "\n")
    }
  }
  
  cat("\n📋 TABLA COMPLETA:\n")
  cat("==================\n")
  print(resultados[, c("Par", "Estado", "Precio", "RSI", "Señal")])
  
  # Recomendaciones
  cat("\n💡 RECOMENDACIONES:\n")
  cat("===================\n")
  
  if (señales_compra > 0) {
    cat("✅ Hay oportunidades de trading hoy\n")
    cat("🎯 Priorizar pares con señal 🚀 COMPRAR\n")
    cat("📊 Verificar manualmente en XTB antes de operar\n")
  } else {
    cat("⏸️ Día de paciencia - No hay señales claras\n")
    cat("📈 Vigilar pares con tendencia alcista\n")
    cat("⏰ Repetir escaneo mañana\n")
  }
  
  cat("🔄 Próximo escaneo recomendado: Mañana a la misma hora\n")
}

# =============================================================================
# FUNCIÓN PARA ANÁLISIS ESPECÍFICO DE UN PAR
# =============================================================================

analisis_detallado <- function(nombre_par) {
  if (!nombre_par %in% names(PARES_FOREX)) {
    cat("❌ Par no encontrado. Pares disponibles:\n")
    cat(paste(names(PARES_FOREX), collapse = ", "), "\n")
    return()
  }
  
  cat("🔍 ANÁLISIS DETALLADO:", nombre_par, "\n")
  cat("================================\n")
  
  simbolo <- PARES_FOREX[[nombre_par]]
  resultado <- analizar_par(nombre_par, simbolo)
  
  cat("Precio actual:", resultado$Precio, "\n")
  cat("SMA20:", resultado$SMA20, "\n")
  cat("SMA50:", resultado$SMA50, "\n")
  cat("RSI:", resultado$RSI, "\n")
  cat("Estado:", resultado$Estado, "\n")
  cat("Señal:", resultado$Señal, "\n")
  
  if (resultado$Prioridad >= 4) {
    cat("\n💰 NIVELES DE TRADING:\n")
    precio_entrada <- resultado$Precio
    stop_loss <- precio_entrada * (1 - PARAMETROS$stop_loss_pct)
    take_profit <- precio_entrada * (1 + PARAMETROS$take_profit_pct)
    
    cat("Entrada:", precio_entrada, "\n")
    cat("Stop Loss (-2%):", round(stop_loss, 5), "\n")
    cat("Take Profit (+4%):", round(take_profit, 5), "\n")
  }
}

# =============================================================================
# EJECUCIÓN PRINCIPAL
# =============================================================================

cat("🎯 Iniciando escaneo de mercados...\n")
resultados_escaneo <- escanear_mercados()
mostrar_resultados(resultados_escaneo)

cat("\n", paste(rep("=", 60), collapse = ""), "\n")
cat("🌍 MULTI-CURRENCY SCANNER COMPLETADO\n")
cat("💡 Para análisis detallado: analisis_detallado('EUR/USD')\n")
cat("🔄 Ejecutar diariamente para encontrar oportunidades\n")
cat(paste(rep("=", 60), collapse = ""), "\n")
