# =============================================================================
# ESTRATEGIA BALANCEADA - LECCIONES APRENDIDAS
# =============================================================================
# Combina lo mejor de todas las versiones anteriores
# Evita la sobre-optimización manteniendo efectividad
# =============================================================================

library(quantmod)
library(TTR)
library(ggplot2)
library(dplyr)

cat("⚖️ ESTRATEGIA BALANCEADA - VERSIÓN DEFINITIVA\n")
cat("=============================================\n\n")

# =============================================================================
# PARÁMETROS BALANCEADOS (BASADOS EN EXPERIENCIA)
# =============================================================================

PARAMETROS_BALANCEADOS <- list(
  # Medias móviles - Compromiso entre sensibilidad y estabilidad
  ma_corta = 20,        # Volver a 20 (funcionó en versión optimizada)
  ma_larga = 50,        # Volver a 50 (funcionó en versión optimizada)
  
  # RSI - Rango amplio pero no excesivo
  rsi_periodo = 14,
  rsi_entrada_min = 45, # Volver a 45 (funcionó)
  rsi_entrada_max = 65, # Límite superior razonable
  rsi_salida_max = 75,  # Volver a 75 (funcionó)
  
  # Gestión de riesgo - Probada
  stop_loss_pct = 0.015,    # 1.5% (funcionó)
  take_profit_pct = 0.03,   # 3% (funcionó)
  
  # Tiempo - Basado en resultados reales
  min_dias_posicion = 2,    # Volver a 2 (menos restrictivo)
  max_dias_posicion = 15,   # Volver a 15 (funcionó)
  
  # Solo UN filtro adicional (no múltiples)
  usar_filtro_volatilidad = TRUE,
  volatilidad_max = 0.008   # Filtro simple de volatilidad
)

cat("📋 PARÁMETROS BALANCEADOS:\n")
cat("==========================\n")
for (param in names(PARAMETROS_BALANCEADOS)) {
  valor <- PARAMETROS_BALANCEADOS[[param]]
  if (is.numeric(valor) && valor < 1 && valor > 0) {
    cat(param, ":", valor * 100, "%\n")
  } else {
    cat(param, ":", valor, "\n")
  }
}
cat("\n")

# =============================================================================
# OBTENER DATOS (VERSIÓN SIMPLE Y CONFIABLE)
# =============================================================================

obtener_datos_balanceados <- function() {
  cat("📥 Descargando datos EUR/USD (3 años)...\n")
  
  fecha_fin <- Sys.Date()
  fecha_inicio <- fecha_fin - 1095  # 3 años
  
  tryCatch({
    datos <- getSymbols("EURUSD=X", 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos <- na.omit(datos)
    
    cat("✅ Datos obtenidos:", nrow(datos), "días\n")
    cat("Período:", as.character(fecha_inicio), "a", as.character(fecha_fin), "\n\n")
    
    return(datos)
    
  }, error = function(e) {
    cat("❌ Error:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# INDICADORES SIMPLES Y EFECTIVOS
# =============================================================================

calcular_indicadores_balanceados <- function(datos) {
  cat("📊 Calculando indicadores balanceados...\n")
  
  precios <- Cl(datos)
  
  # Solo los indicadores esenciales
  ma_corta <- SMA(precios, n = PARAMETROS_BALANCEADOS$ma_corta)
  ma_larga <- SMA(precios, n = PARAMETROS_BALANCEADOS$ma_larga)
  rsi <- RSI(precios, n = PARAMETROS_BALANCEADOS$rsi_periodo)
  
  # UN solo indicador adicional: volatilidad simple
  volatilidad <- runSD(precios, n = 20)
  
  # Dataframe simple
  datos_completos <- data.frame(
    Fecha = index(datos),
    Precio = as.numeric(precios),
    MA_Corta = as.numeric(ma_corta),
    MA_Larga = as.numeric(ma_larga),
    RSI = as.numeric(rsi),
    Volatilidad = as.numeric(volatilidad),
    High = as.numeric(Hi(datos)),
    Low = as.numeric(Lo(datos))
  )
  
  datos_completos <- datos_completos[complete.cases(datos_completos), ]
  
  cat("✅ Indicadores calculados:", nrow(datos_completos), "días válidos\n\n")
  return(datos_completos)
}

# =============================================================================
# ESTRATEGIA BALANCEADA - SIMPLE PERO EFECTIVA
# =============================================================================

ejecutar_estrategia_balanceada <- function(datos) {
  cat("🎯 Ejecutando estrategia balanceada...\n")
  
  if (is.null(datos) || nrow(datos) == 0) {
    return(data.frame())
  }
  
  # Variables de control
  posicion_abierta <- FALSE
  precio_entrada <- 0
  fecha_entrada <- NULL
  dias_en_posicion <- 0
  stop_loss <- 0
  take_profit <- 0
  operaciones <- data.frame()
  
  for (i in 2:nrow(datos)) {
    fecha_actual <- datos$Fecha[i]
    precio_actual <- datos$Precio[i]
    ma_corta_actual <- datos$MA_Corta[i]
    ma_larga_actual <- datos$MA_Larga[i]
    ma_corta_anterior <- datos$MA_Corta[i-1]
    ma_larga_anterior <- datos$MA_Larga[i-1]
    rsi_actual <- datos$RSI[i]
    volatilidad_actual <- datos$Volatilidad[i]
    
    # Verificar datos válidos
    if (any(is.na(c(precio_actual, ma_corta_actual, ma_larga_actual, 
                    ma_corta_anterior, ma_larga_anterior, rsi_actual)))) {
      next
    }
    
    # ENTRADA - CONDICIONES SIMPLES PERO EFECTIVAS
    if (!posicion_abierta) {
      # Condición 1: Cruce alcista de medias móviles
      cruce_alcista <- (ma_corta_anterior <= ma_larga_anterior) && 
                       (ma_corta_actual > ma_larga_actual)
      
      # Condición 2: RSI en rango favorable
      rsi_favorable <- rsi_actual >= PARAMETROS_BALANCEADOS$rsi_entrada_min && 
                       rsi_actual <= PARAMETROS_BALANCEADOS$rsi_entrada_max
      
      # Condición 3: Filtro de volatilidad (OPCIONAL)
      volatilidad_ok <- TRUE
      if (PARAMETROS_BALANCEADOS$usar_filtro_volatilidad && !is.na(volatilidad_actual)) {
        volatilidad_ok <- volatilidad_actual <= PARAMETROS_BALANCEADOS$volatilidad_max
      }
      
      # ENTRADA: Solo 3 condiciones simples
      if (cruce_alcista && rsi_favorable && volatilidad_ok) {
        posicion_abierta <- TRUE
        precio_entrada <- precio_actual
        fecha_entrada <- fecha_actual
        dias_en_posicion <- 0
        stop_loss <- precio_entrada * (1 - PARAMETROS_BALANCEADOS$stop_loss_pct)
        take_profit <- precio_entrada * (1 + PARAMETROS_BALANCEADOS$take_profit_pct)
        
        cat("📈 ENTRADA:", as.character(fecha_actual), 
            "Precio:", round(precio_entrada, 5), 
            "RSI:", round(rsi_actual, 2), 
            "Vol:", round(volatilidad_actual, 4), "\n")
      }
    }
    
    # SALIDA - CONDICIONES PROBADAS
    if (posicion_abierta) {
      dias_en_posicion <- dias_en_posicion + 1
      salida <- FALSE
      motivo_salida <- ""
      
      # Condición 1: RSI sobrecompra (con filtro de tiempo)
      if (rsi_actual >= PARAMETROS_BALANCEADOS$rsi_salida_max && 
          dias_en_posicion >= PARAMETROS_BALANCEADOS$min_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "RSI_Sobrecompra"
      }
      
      # Condición 2: Precio bajo MA corta (con filtro de tiempo)
      if (precio_actual < ma_corta_actual && 
          dias_en_posicion >= PARAMETROS_BALANCEADOS$min_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "Ruptura_Tendencia"
      }
      
      # Condición 3: Stop Loss
      if (precio_actual <= stop_loss) {
        salida <- TRUE
        motivo_salida <- "Stop_Loss"
      }
      
      # Condición 4: Take Profit
      if (precio_actual >= take_profit) {
        salida <- TRUE
        motivo_salida <- "Take_Profit"
      }
      
      # Condición 5: Tiempo máximo
      if (dias_en_posicion >= PARAMETROS_BALANCEADOS$max_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "Tiempo_Maximo"
      }
      
      # EJECUTAR SALIDA
      if (salida) {
        precio_salida <- precio_actual
        rendimiento <- (precio_salida - precio_entrada) / precio_entrada
        rendimiento_pct <- rendimiento * 100
        
        # Registrar operación
        nueva_operacion <- data.frame(
          Entrada_Fecha = fecha_entrada,
          Entrada_Precio = precio_entrada,
          Salida_Fecha = fecha_actual,
          Salida_Precio = precio_salida,
          Dias_Posicion = dias_en_posicion,
          Rendimiento_Pct = rendimiento_pct,
          Motivo_Salida = motivo_salida,
          Ganadora = rendimiento > 0,
          RSI_Entrada = rsi_actual,  # Simplificado
          RSI_Salida = rsi_actual
        )
        
        operaciones <- rbind(operaciones, nueva_operacion)
        
        cat("📉 SALIDA:", as.character(fecha_actual), 
            "Precio:", round(precio_salida, 5), 
            "Días:", dias_en_posicion,
            "Rendimiento:", round(rendimiento_pct, 2), "%",
            "Motivo:", motivo_salida, "\n")
        
        # Reset
        posicion_abierta <- FALSE
        precio_entrada <- 0
        fecha_entrada <- NULL
        dias_en_posicion <- 0
      }
    }
  }
  
  cat("✅ Estrategia balanceada completada:", nrow(operaciones), "operaciones\n\n")
  return(operaciones)
}

# =============================================================================
# ANÁLISIS COMPARATIVO
# =============================================================================

analizar_resultados_balanceados <- function(resultados) {
  if (nrow(resultados) == 0) {
    cat("❌ No se generaron operaciones\n")
    return()
  }
  
  cat("🎉 RESULTADOS DE LA ESTRATEGIA BALANCEADA:\n")
  cat("==========================================\n")
  
  # Métricas principales
  total_ops <- nrow(resultados)
  ops_ganadoras <- sum(resultados$Ganadora)
  tasa_exito <- (ops_ganadoras / total_ops) * 100
  rendimiento_total <- sum(resultados$Rendimiento_Pct)
  rendimiento_promedio <- mean(resultados$Rendimiento_Pct)
  mejor_operacion <- max(resultados$Rendimiento_Pct)
  peor_operacion <- min(resultados$Rendimiento_Pct)
  dias_promedio <- mean(resultados$Dias_Posicion)
  
  cat("📊 MÉTRICAS PRINCIPALES:\n")
  cat("Total operaciones:", total_ops, "\n")
  cat("Operaciones ganadoras:", ops_ganadoras, "\n")
  cat("Tasa de éxito:", round(tasa_exito, 1), "%\n")
  cat("Rendimiento total:", round(rendimiento_total, 2), "%\n")
  cat("Rendimiento promedio:", round(rendimiento_promedio, 2), "%\n")
  cat("Mejor operación:", round(mejor_operacion, 2), "%\n")
  cat("Peor operación:", round(peor_operacion, 2), "%\n")
  cat("Días promedio en posición:", round(dias_promedio, 1), "\n")
  
  # Análisis por motivo de salida
  cat("\n📋 MOTIVOS DE SALIDA:\n")
  motivos <- table(resultados$Motivo_Salida)
  for (motivo in names(motivos)) {
    cat(motivo, ":", motivos[motivo], "operaciones\n")
  }
  
  # Evaluación de la estrategia
  cat("\n🎯 EVALUACIÓN:\n")
  if (tasa_exito >= 50) {
    cat("✅ Tasa de éxito: EXCELENTE\n")
  } else if (tasa_exito >= 40) {
    cat("⚠️ Tasa de éxito: BUENA\n")
  } else {
    cat("❌ Tasa de éxito: NECESITA MEJORAS\n")
  }
  
  if (rendimiento_total > 2) {
    cat("✅ Rendimiento: MUY BUENO\n")
  } else if (rendimiento_total > 0) {
    cat("⚠️ Rendimiento: POSITIVO\n")
  } else {
    cat("❌ Rendimiento: NEGATIVO\n")
  }
  
  cat("\n📈 DETALLE DE OPERACIONES:\n")
  print(resultados[, c("Entrada_Fecha", "Salida_Fecha", "Dias_Posicion", 
                      "Rendimiento_Pct", "Motivo_Salida", "Ganadora")])
}

# =============================================================================
# EJECUCIÓN DE LA ESTRATEGIA BALANCEADA
# =============================================================================

# Ejecutar estrategia
datos_balanceados <- obtener_datos_balanceados()

if (!is.null(datos_balanceados)) {
  datos_con_indicadores <- calcular_indicadores_balanceados(datos_balanceados)
  resultados_balanceados <- ejecutar_estrategia_balanceada(datos_con_indicadores)
  analizar_resultados_balanceados(resultados_balanceados)
} else {
  cat("❌ No se pudieron obtener datos\n")
}

cat("\n⚖️ ESTRATEGIA BALANCEADA COMPLETADA\n")
cat("===================================\n")
cat("💡 Esta versión busca el equilibrio entre:\n")
cat("   - Suficientes operaciones (no muy restrictiva)\n")
cat("   - Buena tasa de éxito (filtros efectivos)\n")
cat("   - Simplicidad (evita sobre-optimización)\n")
