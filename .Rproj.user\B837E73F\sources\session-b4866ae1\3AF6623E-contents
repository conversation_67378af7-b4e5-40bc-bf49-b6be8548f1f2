# =============================================================================
# XTB TRADING ASSISTANT - ANÁLISIS DIARIO PARA TRADING
# =============================================================================
# Versión específica para usar junto con cuenta demo XTB
# Proporciona análisis diario y señales de trading
# =============================================================================

library(quantmod)
library(TTR)

cat("🤖 XTB TRADING ASSISTANT\n")
cat("========================\n")
cat("Análisis diario para estrategia EUR/USD\n\n")

# =============================================================================
# PARÁMETROS DE LA ESTRATEGIA (MISMOS QUE VALIDAMOS)
# =============================================================================

PARAMETROS <- list(
  ma_corta = 20,
  ma_larga = 50,
  rsi_periodo = 14,
  rsi_entrada_min = 45,
  rsi_entrada_max = 65,
  rsi_salida_max = 75,
  stop_loss_pct = 0.02,
  take_profit_pct = 0.04,
  min_dias_posicion = 5,
  max_dias_posicion = 25,
  dias_confirmacion = 2,
  volatilidad_max = 0.008
)

# =============================================================================
# FUNCIÓN PARA ANÁLISIS DIARIO
# =============================================================================

analisis_diario_xtb <- function() {
  cat("📥 Descargando datos actualizados de EUR/USD...\n")
  
  # Obtener datos recientes
  fecha_fin <- Sys.Date()
  fecha_inicio <- fecha_fin - 365  # 1 año para análisis
  
  tryCatch({
    datos <- getSymbols("EURUSD=X", 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos <- na.omit(datos)
    
    # Calcular indicadores
    precios <- Cl(datos)
    ma_corta <- SMA(precios, n = PARAMETROS$ma_corta)
    ma_larga <- SMA(precios, n = PARAMETROS$ma_larga)
    rsi <- RSI(precios, n = PARAMETROS$rsi_periodo)
    volatilidad <- runSD(precios, n = 20)
    
    # Crear dataframe
    df <- data.frame(
      Fecha = index(datos),
      Precio = as.numeric(precios),
      MA_Corta = as.numeric(ma_corta),
      MA_Larga = as.numeric(ma_larga),
      RSI = as.numeric(rsi),
      Volatilidad = as.numeric(volatilidad)
    )
    
    df <- df[complete.cases(df), ]
    
    # Análisis de los últimos días
    n <- nrow(df)
    if (n < 5) {
      cat("❌ Datos insuficientes\n")
      return()
    }
    
    # Datos de hoy y ayer
    hoy <- df[n, ]
    ayer <- df[n-1, ]
    
    cat("✅ Datos actualizados hasta:", as.character(hoy$Fecha), "\n\n")
    
    # =============================================================================
    # ANÁLISIS DE SEÑALES
    # =============================================================================
    
    cat("📊 ANÁLISIS DE MERCADO ACTUAL\n")
    cat("=============================\n")
    cat("Precio actual EUR/USD:", round(hoy$Precio, 5), "\n")
    cat("SMA20:", round(hoy$MA_Corta, 5), "\n")
    cat("SMA50:", round(hoy$MA_Larga, 5), "\n")
    cat("RSI:", round(hoy$RSI, 2), "\n")
    cat("Volatilidad:", round(hoy$Volatilidad, 4), "\n\n")
    
    # Verificar cruce
    cruce_alcista <- (ayer$MA_Corta <= ayer$MA_Larga) && (hoy$MA_Corta > hoy$MA_Larga)
    rsi_favorable <- hoy$RSI >= PARAMETROS$rsi_entrada_min && hoy$RSI <= PARAMETROS$rsi_entrada_max
    volatilidad_ok <- is.na(hoy$Volatilidad) || hoy$Volatilidad <= PARAMETROS$volatilidad_max
    
    cat("🔍 VERIFICACIÓN DE SEÑALES\n")
    cat("==========================\n")
    
    if (cruce_alcista) {
      cat("✅ CRUCE ALCISTA DETECTADO: SMA20 cruza arriba de SMA50\n")
    } else {
      cat("❌ No hay cruce alcista hoy\n")
      cat("   SMA20 vs SMA50:", ifelse(hoy$MA_Corta > hoy$MA_Larga, "SMA20 arriba", "SMA20 abajo"), "\n")
    }
    
    if (rsi_favorable) {
      cat("✅ RSI EN RANGO FAVORABLE:", round(hoy$RSI, 2), "(entre 45-65)\n")
    } else {
      cat("❌ RSI fuera de rango:", round(hoy$RSI, 2), "\n")
      if (hoy$RSI < 45) cat("   → RSI muy bajo (esperar subida)\n")
      if (hoy$RSI > 65) cat("   → RSI muy alto (entrada tardía)\n")
    }
    
    if (volatilidad_ok) {
      cat("✅ VOLATILIDAD NORMAL:", round(hoy$Volatilidad, 4), "\n")
    } else {
      cat("⚠️ VOLATILIDAD ALTA:", round(hoy$Volatilidad, 4), "(>0.008)\n")
    }
    
    # DECISIÓN FINAL
    cat("\n🎯 DECISIÓN DE TRADING\n")
    cat("======================\n")
    
    if (cruce_alcista && rsi_favorable && volatilidad_ok) {
      cat("🚀 ¡SEÑAL DE COMPRA CONFIRMADA!\n")
      cat("📈 ACCIÓN RECOMENDADA: COMPRAR EUR/USD\n\n")
      
      # Calcular niveles
      precio_entrada <- hoy$Precio
      stop_loss <- precio_entrada * (1 - PARAMETROS$stop_loss_pct)
      take_profit <- precio_entrada * (1 + PARAMETROS$take_profit_pct)
      
      cat("💰 NIVELES PARA XTB:\n")
      cat("Precio entrada:", round(precio_entrada, 5), "\n")
      cat("Stop Loss (-2%):", round(stop_loss, 5), "\n")
      cat("Take Profit (+4%):", round(take_profit, 5), "\n")
      cat("Tamaño sugerido: 0.01 lotes (demo)\n\n")
      
      cat("📋 CHECKLIST PARA XTB:\n")
      cat("□ Abrir xStation 5\n")
      cat("□ Verificar gráfico EUR/USD diario\n")
      cat("□ Confirmar cruce visual de medias\n")
      cat("□ Verificar RSI entre 45-65\n")
      cat("□ Ejecutar compra con niveles calculados\n")
      cat("□ Anotar operación en registro\n")
      
    } else {
      cat("⏸️ NO HAY SEÑAL DE ENTRADA HOY\n")
      cat("📊 ACCIÓN RECOMENDADA: ESPERAR\n\n")
      
      cat("🔍 RAZONES:\n")
      if (!cruce_alcista) cat("- No hay cruce alcista de medias\n")
      if (!rsi_favorable) cat("- RSI fuera del rango 45-65\n")
      if (!volatilidad_ok) cat("- Volatilidad demasiado alta\n")
      
      cat("\n⏰ PRÓXIMA REVISIÓN: Mañana a la misma hora\n")
    }
    
    # =============================================================================
    # ANÁLISIS DE POSICIÓN EXISTENTE (SI LA HAY)
    # =============================================================================
    
    cat("\n🔄 GESTIÓN DE POSICIÓN EXISTENTE\n")
    cat("=================================\n")
    cat("Si tienes una posición abierta, verifica:\n\n")
    
    # Condiciones de salida
    salida_rsi <- hoy$RSI >= PARAMETROS$rsi_salida_max
    precio_bajo_ma <- hoy$Precio < hoy$MA_Corta
    
    if (salida_rsi) {
      cat("🚨 RSI SOBRECOMPRA:", round(hoy$RSI, 2), "≥ 75\n")
      cat("📉 CONSIDERAR SALIDA (si han pasado +5 días)\n")
    }
    
    if (precio_bajo_ma) {
      cat("⚠️ PRECIO BAJO SMA20:", round(hoy$Precio, 5), "<", round(hoy$MA_Corta, 5), "\n")
      cat("📉 VIGILAR RUPTURA (confirmar 2 días consecutivos)\n")
    }
    
    if (!salida_rsi && !precio_bajo_ma) {
      cat("✅ Posición puede continuar\n")
      cat("📈 Precio arriba de SMA20 y RSI < 75\n")
    }
    
    # =============================================================================
    # RESUMEN EJECUTIVO
    # =============================================================================
    
    cat("\n📋 RESUMEN EJECUTIVO\n")
    cat("====================\n")
    cat("Fecha:", as.character(Sys.Date()), "\n")
    cat("EUR/USD:", round(hoy$Precio, 5), "\n")
    cat("Tendencia:", ifelse(hoy$MA_Corta > hoy$MA_Larga, "ALCISTA", "BAJISTA"), "\n")
    cat("RSI:", round(hoy$RSI, 2), ifelse(hoy$RSI > 70, "(Sobrecompra)", 
                                        ifelse(hoy$RSI < 30, "(Sobreventa)", "(Normal)")), "\n")
    
    if (cruce_alcista && rsi_favorable && volatilidad_ok) {
      cat("🎯 ACCIÓN: COMPRAR\n")
    } else {
      cat("🎯 ACCIÓN: ESPERAR\n")
    }
    
    cat("\n⏰ Próximo análisis: Mañana\n")
    cat("📱 Recordatorio: Revisar posiciones existentes\n")
    
  }, error = function(e) {
    cat("❌ Error obteniendo datos:", e$message, "\n")
    cat("💡 Soluciones:\n")
    cat("- Verificar conexión a internet\n")
    cat("- Intentar más tarde\n")
    cat("- Usar análisis manual en XTB\n")
  })
}

# =============================================================================
# FUNCIÓN PARA ANÁLISIS SEMANAL
# =============================================================================

resumen_semanal <- function() {
  cat("\n📊 RESUMEN SEMANAL\n")
  cat("==================\n")
  cat("Ejecuta esta función los viernes para revisar la semana\n")
  cat("Próximamente: análisis de rendimiento semanal\n")
}

# =============================================================================
# EJECUCIÓN PRINCIPAL
# =============================================================================

cat("🎯 Ejecutando análisis diario...\n\n")
analisis_diario_xtb()

cat("\n", paste(rep("=", 50), collapse = ""), "\n")
cat("🤖 XTB TRADING ASSISTANT COMPLETADO\n")
cat("💡 Ejecuta este script cada mañana antes de operar\n")
cat("📞 Para análisis semanal: resumen_semanal()\n")
cat(paste(rep("=", 50), collapse = ""), "\n")
