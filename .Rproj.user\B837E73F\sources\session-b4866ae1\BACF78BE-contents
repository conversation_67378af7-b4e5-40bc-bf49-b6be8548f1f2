# =============================================================================
# ESTRATEGIA FINAL MEJORADA - BASADA EN RESULTADOS REALES
# =============================================================================
# Optimizada con base en el análisis de las 4 operaciones anteriores
# Ajustes para mejorar la tasa de éxito del 25% actual
# =============================================================================

library(quantmod)
library(TTR)
library(ggplot2)
library(dplyr)

cat("🎯 ESTRATEGIA FINAL MEJORADA\n")
cat("============================\n\n")

# =============================================================================
# PARÁMETROS FINALES OPTIMIZADOS
# =============================================================================

PARAMETROS_FINALES <- list(
  # Medias móviles (ajustadas para mejor timing)
  ma_corta = 15,        # Más sensible para entradas tempranas
  ma_larga = 40,        # Reducido para más señales
  
  # RSI (ajustado basado en resultados)
  rsi_periodo = 14,
  rsi_entrada_min = 40, # Más permisivo (era 45)
  rsi_entrada_max = 60, # Límite superior para evitar entradas tardías
  rsi_salida_max = 80,  # Más permisivo (era 75)
  
  # Gestión de riesgo mejorada
  stop_loss_pct = 0.012,    # 1.2% (más ajustado)
  take_profit_pct = 0.025,  # 2.5% (más realista)
  
  # Filtros de tiempo optimizados
  min_dias_posicion = 3,    # Mínimo 3 días (era 2)
  max_dias_posicion = 20,   # Máximo 20 días (era 15)
  
  # Nuevos filtros basados en análisis
  confirmar_tendencia = TRUE,    # Confirmar tendencia antes de entrada
  filtro_volatilidad = TRUE,     # Evitar mercados muy volátiles
  trailing_stop = TRUE,          # Stop loss dinámico
  trailing_stop_pct = 0.008      # 0.8% para trailing stop
)

cat("📋 PARÁMETROS FINALES OPTIMIZADOS:\n")
cat("===================================\n")
for (param in names(PARAMETROS_FINALES)) {
  valor <- PARAMETROS_FINALES[[param]]
  if (is.numeric(valor) && valor < 1 && valor > 0) {
    cat(param, ":", valor * 100, "%\n")
  } else {
    cat(param, ":", valor, "\n")
  }
}
cat("\n")

# =============================================================================
# FUNCIÓN MEJORADA PARA OBTENER Y PREPARAR DATOS
# =============================================================================

preparar_datos_finales <- function() {
  cat("📥 Obteniendo datos para estrategia final...\n")
  
  # Obtener más datos para mejor análisis
  fecha_fin <- Sys.Date()
  fecha_inicio <- fecha_fin - 1095  # 3 años en lugar de 2
  
  tryCatch({
    datos <- getSymbols("EURUSD=X", 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    
    # Limpieza avanzada de datos
    datos_limpios <- na.omit(datos)
    
    # Verificar calidad de datos
    if (nrow(datos_limpios) < 500) {
      cat("⚠️ Advertencia: Pocos datos disponibles\n")
    }
    
    cat("✅ Datos preparados:", nrow(datos_limpios), "días\n")
    cat("Período:", as.character(fecha_inicio), "a", as.character(fecha_fin), "\n\n")
    
    return(datos_limpios)
    
  }, error = function(e) {
    cat("❌ Error obteniendo datos:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# CÁLCULO DE INDICADORES AVANZADOS
# =============================================================================

calcular_indicadores_finales <- function(datos) {
  cat("📊 Calculando indicadores avanzados...\n")
  
  precios <- Cl(datos)
  
  # Indicadores principales
  ma_corta <- SMA(precios, n = PARAMETROS_FINALES$ma_corta)
  ma_larga <- SMA(precios, n = PARAMETROS_FINALES$ma_larga)
  rsi <- RSI(precios, n = PARAMETROS_FINALES$rsi_periodo)
  
  # Indicadores adicionales para filtros
  atr <- ATR(HLC(datos), n = 14)$atr  # Average True Range para volatilidad
  macd <- MACD(precios)$macd          # MACD para confirmación de tendencia
  bb <- BBands(precios, n = 20)       # Bandas de Bollinger
  
  # Crear dataframe completo
  datos_completos <- data.frame(
    Fecha = index(datos),
    Precio = as.numeric(precios),
    MA_Corta = as.numeric(ma_corta),
    MA_Larga = as.numeric(ma_larga),
    RSI = as.numeric(rsi),
    ATR = as.numeric(atr),
    MACD = as.numeric(macd),
    BB_Upper = as.numeric(bb[, "up"]),
    BB_Lower = as.numeric(bb[, "dn"]),
    BB_Middle = as.numeric(bb[, "mavg"]),
    High = as.numeric(Hi(datos)),
    Low = as.numeric(Lo(datos)),
    Open = as.numeric(Op(datos)),
    Volume = as.numeric(Vo(datos))
  )
  
  # Eliminar NAs
  datos_completos <- datos_completos[complete.cases(datos_completos), ]
  
  cat("✅ Indicadores calculados:", nrow(datos_completos), "días válidos\n\n")
  return(datos_completos)
}

# =============================================================================
# ESTRATEGIA FINAL CON FILTROS AVANZADOS
# =============================================================================

ejecutar_estrategia_final <- function(datos) {
  cat("🚀 Ejecutando estrategia final mejorada...\n")
  
  if (is.null(datos) || nrow(datos) == 0) {
    return(data.frame())
  }
  
  # Variables de control
  posicion_abierta <- FALSE
  precio_entrada <- 0
  fecha_entrada <- NULL
  dias_en_posicion <- 0
  stop_loss <- 0
  take_profit <- 0
  trailing_stop_precio <- 0
  operaciones <- data.frame()
  
  for (i in 3:nrow(datos)) {  # Empezar en 3 para tener más historia
    fecha_actual <- datos$Fecha[i]
    precio_actual <- datos$Precio[i]
    
    # Extraer indicadores actuales
    ma_corta_actual <- datos$MA_Corta[i]
    ma_larga_actual <- datos$MA_Larga[i]
    ma_corta_anterior <- datos$MA_Corta[i-1]
    ma_larga_anterior <- datos$MA_Larga[i-1]
    rsi_actual <- datos$RSI[i]
    atr_actual <- datos$ATR[i]
    macd_actual <- datos$MACD[i]
    
    # Verificar datos válidos
    if (any(is.na(c(precio_actual, ma_corta_actual, ma_larga_actual, 
                    ma_corta_anterior, ma_larga_anterior, rsi_actual)))) {
      next
    }
    
    # LÓGICA DE ENTRADA MEJORADA
    if (!posicion_abierta) {
      # Condición 1: Cruce alcista de medias móviles
      cruce_alcista <- (ma_corta_anterior <= ma_larga_anterior) && 
                       (ma_corta_actual > ma_larga_actual)
      
      # Condición 2: RSI en rango óptimo
      rsi_optimo <- rsi_actual >= PARAMETROS_FINALES$rsi_entrada_min && 
                    rsi_actual <= PARAMETROS_FINALES$rsi_entrada_max
      
      # Condición 3: Confirmación de tendencia con MACD
      macd_positivo <- is.na(macd_actual) || macd_actual > 0
      
      # Condición 4: Filtro de volatilidad con ATR
      volatilidad_normal <- is.na(atr_actual) || atr_actual < 0.005
      
      # Condición 5: Precio no en extremos de Bandas de Bollinger
      precio_no_extremo <- precio_actual < datos$BB_Upper[i] && 
                          precio_actual > datos$BB_Lower[i]
      
      # Condición 6: Tendencia de MA corta
      ma_corta_subiendo <- datos$MA_Corta[i] > datos$MA_Corta[i-1]
      
      # ENTRADA: Todas las condiciones deben cumplirse
      if (cruce_alcista && rsi_optimo && macd_positivo && 
          volatilidad_normal && precio_no_extremo && ma_corta_subiendo) {
        
        posicion_abierta <- TRUE
        precio_entrada <- precio_actual
        fecha_entrada <- fecha_actual
        dias_en_posicion <- 0
        stop_loss <- precio_entrada * (1 - PARAMETROS_FINALES$stop_loss_pct)
        take_profit <- precio_entrada * (1 + PARAMETROS_FINALES$take_profit_pct)
        trailing_stop_precio <- precio_entrada * (1 - PARAMETROS_FINALES$trailing_stop_pct)
        
        cat("📈 ENTRADA:", as.character(fecha_actual), 
            "Precio:", round(precio_entrada, 5), 
            "RSI:", round(rsi_actual, 2),
            "MACD:", round(macd_actual, 6), "\n")
      }
    }
    
    # LÓGICA DE SALIDA MEJORADA
    if (posicion_abierta) {
      dias_en_posicion <- dias_en_posicion + 1
      salida <- FALSE
      motivo_salida <- ""
      
      # Actualizar trailing stop si el precio sube
      if (PARAMETROS_FINALES$trailing_stop && precio_actual > precio_entrada) {
        nuevo_trailing <- precio_actual * (1 - PARAMETROS_FINALES$trailing_stop_pct)
        if (nuevo_trailing > trailing_stop_precio) {
          trailing_stop_precio <- nuevo_trailing
        }
      }
      
      # Condición 1: RSI sobrecompra
      if (rsi_actual >= PARAMETROS_FINALES$rsi_salida_max && 
          dias_en_posicion >= PARAMETROS_FINALES$min_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "RSI_Sobrecompra"
      }
      
      # Condición 2: Precio bajo MA corta
      if (precio_actual < ma_corta_actual && 
          dias_en_posicion >= PARAMETROS_FINALES$min_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "Ruptura_Tendencia"
      }
      
      # Condición 3: Stop Loss fijo
      if (precio_actual <= stop_loss) {
        salida <- TRUE
        motivo_salida <- "Stop_Loss"
      }
      
      # Condición 4: Trailing Stop
      if (PARAMETROS_FINALES$trailing_stop && precio_actual <= trailing_stop_precio) {
        salida <- TRUE
        motivo_salida <- "Trailing_Stop"
      }
      
      # Condición 5: Take Profit
      if (precio_actual >= take_profit) {
        salida <- TRUE
        motivo_salida <- "Take_Profit"
      }
      
      # Condición 6: Tiempo máximo
      if (dias_en_posicion >= PARAMETROS_FINALES$max_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "Tiempo_Maximo"
      }
      
      # EJECUTAR SALIDA
      if (salida) {
        precio_salida <- precio_actual
        rendimiento <- (precio_salida - precio_entrada) / precio_entrada
        rendimiento_pct <- rendimiento * 100
        
        # Registrar operación
        nueva_operacion <- data.frame(
          Entrada_Fecha = fecha_entrada,
          Entrada_Precio = precio_entrada,
          Salida_Fecha = fecha_actual,
          Salida_Precio = precio_salida,
          Dias_Posicion = dias_en_posicion,
          Rendimiento_Pct = rendimiento_pct,
          Motivo_Salida = motivo_salida,
          Ganadora = rendimiento > 0,
          RSI_Entrada = datos$RSI[which(datos$Fecha == fecha_entrada)[1]],
          RSI_Salida = rsi_actual,
          Max_Ganancia = ((max(datos$Precio[which(datos$Fecha >= fecha_entrada & 
                                                 datos$Fecha <= fecha_actual)]) - precio_entrada) / precio_entrada) * 100
        )
        
        operaciones <- rbind(operaciones, nueva_operacion)
        
        cat("📉 SALIDA:", as.character(fecha_actual), 
            "Precio:", round(precio_salida, 5), 
            "Días:", dias_en_posicion,
            "Rendimiento:", round(rendimiento_pct, 2), "%",
            "Motivo:", motivo_salida, "\n")
        
        # Reset
        posicion_abierta <- FALSE
        precio_entrada <- 0
        fecha_entrada <- NULL
        dias_en_posicion <- 0
        trailing_stop_precio <- 0
      }
    }
  }
  
  cat("✅ Estrategia final completada:", nrow(operaciones), "operaciones\n\n")
  return(operaciones)
}

# =============================================================================
# EJECUCIÓN Y ANÁLISIS FINAL
# =============================================================================

# Ejecutar estrategia final
datos_finales <- preparar_datos_finales()

if (!is.null(datos_finales)) {
  datos_con_indicadores_finales <- calcular_indicadores_finales(datos_finales)
  resultados_finales <- ejecutar_estrategia_final(datos_con_indicadores_finales)
  
  if (nrow(resultados_finales) > 0) {
    cat("🎉 RESULTADOS FINALES DE LA ESTRATEGIA:\n")
    cat("======================================\n")
    
    # Métricas principales
    total_ops <- nrow(resultados_finales)
    ops_ganadoras <- sum(resultados_finales$Ganadora)
    tasa_exito <- (ops_ganadoras / total_ops) * 100
    rendimiento_total <- sum(resultados_finales$Rendimiento_Pct)
    rendimiento_promedio <- mean(resultados_finales$Rendimiento_Pct)
    mejor_operacion <- max(resultados_finales$Rendimiento_Pct)
    peor_operacion <- min(resultados_finales$Rendimiento_Pct)
    dias_promedio <- mean(resultados_finales$Dias_Posicion)
    
    cat("📊 MÉTRICAS PRINCIPALES:\n")
    cat("Total operaciones:", total_ops, "\n")
    cat("Operaciones ganadoras:", ops_ganadoras, "\n")
    cat("Tasa de éxito:", round(tasa_exito, 1), "%\n")
    cat("Rendimiento total:", round(rendimiento_total, 2), "%\n")
    cat("Rendimiento promedio:", round(rendimiento_promedio, 2), "%\n")
    cat("Mejor operación:", round(mejor_operacion, 2), "%\n")
    cat("Peor operación:", round(peor_operacion, 2), "%\n")
    cat("Días promedio en posición:", round(dias_promedio, 1), "\n")
    
    # Análisis por motivo de salida
    cat("\n📋 ANÁLISIS POR MOTIVO DE SALIDA:\n")
    motivos <- table(resultados_finales$Motivo_Salida)
    for (motivo in names(motivos)) {
      cat(motivo, ":", motivos[motivo], "operaciones\n")
    }
    
    cat("\n📈 DETALLE COMPLETO DE OPERACIONES:\n")
    print(resultados_finales[, c("Entrada_Fecha", "Salida_Fecha", "Dias_Posicion", 
                                "Rendimiento_Pct", "Motivo_Salida", "Ganadora")])
    
  } else {
    cat("❌ No se generaron operaciones con la estrategia final\n")
  }
} else {
  cat("❌ No se pudieron obtener datos\n")
}

cat("\n🏆 ESTRATEGIA FINAL COMPLETADA\n")
cat("===============================\n")
