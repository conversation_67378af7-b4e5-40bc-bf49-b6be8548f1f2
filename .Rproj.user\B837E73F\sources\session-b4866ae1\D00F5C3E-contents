# Estrategia de Trading: Cruce de Medias Móviles + RSI

## 📋 Descripción de la Estrategia

Esta estrategia combina dos indicadores técnicos populares para generar señales de compra en el par EUR/USD:

### 🎯 Condiciones de Entrada
- **Cruce Alcista**: La media móvil de 50 días cruza por encima de la media móvil de 200 días
- **Confirmación RSI**: El RSI debe estar por encima de 50 (momentum positivo)

### 🚪 Condiciones de Salida
- **RSI Sobrecompra**: Cuando el RSI alcanza 70 o más
- **Ruptura de Tendencia**: Cuando el precio cae por debajo de la MA50
- **Stop Loss**: 2% por debajo del precio de entrada
- **Take Profit**: 5% por encima del precio de entrada

## 🛠️ Instalación y Configuración

### Requisitos Previos
Asegúrate de tener R instalado en tu sistema. Luego instala las librerías necesarias:

```r
# Instalar librerías requeridas
install.packages(c(
  "quantmod",    # Para datos financieros
  "TTR",         # Indicadores técnicos
  "ggplot2",     # Gráficos
  "dplyr",       # Manipulación de datos
  "gridExtra",   # Múltiples gráficos
  "scales"       # Formateo de escalas
))
```

### 🚀 Ejecución del Script

1. **Abrir R o RStudio**
2. **Establecer directorio de trabajo**:
   ```r
   setwd("ruta/a/tu/directorio")
   ```
3. **Ejecutar el script**:
   ```r
   source("estrategia_trading_eurusd.R")
   ```

## 📊 Resultados y Análisis

El script genera automáticamente:

### 📈 Gráficos
1. **Precio y Medias Móviles**: Visualización del EUR/USD con MA50 y MA200
2. **RSI**: Índice de Fuerza Relativa con niveles clave (30, 50, 70)
3. **Rendimientos Acumulados**: Evolución de la rentabilidad de la estrategia
4. **Distribución de Rendimientos**: Histograma de operaciones ganadoras vs perdedoras

### 📋 Reportes
- **Estadísticas Generales**: Total de operaciones, tasa de éxito, rendimientos
- **Análisis de Riesgo**: Drawdown máximo, ratio riesgo-beneficio
- **Tabla Detallada**: Todas las operaciones con fechas, precios y resultados
- **Recomendaciones**: Sugerencias para optimizar la estrategia

## ⚠️ Consideraciones Importantes

### Limitaciones
- **Datos Históricos**: Los resultados pasados no garantizan rendimientos futuros
- **Costos de Transacción**: No se incluyen spreads ni comisiones
- **Slippage**: No se considera el deslizamiento de precios
- **Liquidez**: Asume ejecución perfecta de órdenes

### Factores de Riesgo
- **Mercados Laterales**: La estrategia puede generar señales falsas en mercados sin tendencia
- **Volatilidad**: Períodos de alta volatilidad pueden activar stops prematuramente
- **Noticias**: Eventos fundamentales pueden invalidar el análisis técnico

## 🔧 Personalización

### Parámetros Modificables
Puedes ajustar los siguientes parámetros en el código:

```r
# Períodos de medias móviles
ma_50 <- SMA(precios, n = 50)   # Cambiar 50 por otro valor
ma_200 <- SMA(precios, n = 200) # Cambiar 200 por otro valor

# Período del RSI
rsi <- RSI(precios, n = 14)     # Cambiar 14 por otro valor

# Niveles de RSI
rsi_entrada <- 50               # Nivel mínimo para entrada
rsi_salida <- 70               # Nivel de sobrecompra para salida

# Gestión de riesgo
stop_loss_pct <- 0.02          # 2% - Cambiar según tolerancia al riesgo
take_profit_pct <- 0.05        # 5% - Cambiar según objetivos de ganancia
```

## 📞 Solución de Problemas

### Error: "No se pudieron descargar datos"
**Posibles causas y soluciones:**
1. **Sin conexión a internet**: Verificar conectividad
2. **Yahoo Finance temporalmente no disponible**: Intentar más tarde
3. **Símbolo incorrecto**: Verificar que "EURUSD=X" esté disponible
4. **Firewall/Proxy**: Configurar acceso a internet para R

### Error: "Paquete no encontrado"
```r
# Reinstalar paquetes problemáticos
install.packages("nombre_del_paquete", dependencies = TRUE)
```

### Error: "No hay operaciones"
**Posibles causas:**
- Período de análisis muy corto
- Criterios de entrada muy restrictivos
- Mercado sin tendencias claras durante el período

## 📚 Recursos Adicionales

### Documentación de Indicadores
- **SMA (Simple Moving Average)**: Media móvil simple
- **RSI (Relative Strength Index)**: Oscilador de momentum (0-100)

### Librerías Utilizadas
- **quantmod**: [Documentación oficial](https://cran.r-project.org/package=quantmod)
- **TTR**: [Technical Trading Rules](https://cran.r-project.org/package=TTR)
- **ggplot2**: [Grammar of Graphics](https://ggplot2.tidyverse.org/)

## ⚖️ Disclaimer

**ADVERTENCIA DE RIESGO**: Este código es solo para fines educativos y de investigación. El trading de divisas conlleva un alto riesgo de pérdida. Nunca inviertas dinero que no puedas permitirte perder. Siempre consulta con un asesor financiero profesional antes de tomar decisiones de inversión.

---

**Desarrollado para análisis educativo de estrategias de trading algorítmico**
