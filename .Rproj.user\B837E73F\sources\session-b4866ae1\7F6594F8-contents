# =============================================================================
# ESTRATEGIA DE TENDENCIA SIMPLE - SIN ERRORES DE DIMENSIONES
# =============================================================================
# Versión simplificada que evita problemas técnicos
# Enfoque: Permitir tendencias largas con confirmación de salida
# =============================================================================

library(quantmod)
library(TTR)
library(ggplot2)
library(dplyr)

cat("📈 ESTRATEGIA DE TENDENCIA SIMPLE\n")
cat("=================================\n\n")

# =============================================================================
# PARÁMETROS PARA TENDENCIAS LARGAS
# =============================================================================

PARAMETROS <- list(
  # Medias móviles
  ma_corta = 20,
  ma_larga = 50,
  
  # RSI
  rsi_periodo = 14,
  rsi_entrada_min = 45,
  rsi_entrada_max = 65,
  rsi_salida_max = 75,
  
  # Gestión de riesgo para tendencias largas
  stop_loss_pct = 0.02,     # 2%
  take_profit_pct = 0.04,   # 4%
  
  # TIEMPO EXTENDIDO - Clave del descubrimiento
  min_dias_posicion = 5,    # Mínimo 5 días
  max_dias_posicion = 25,   # Máximo 25 días
  
  # Confirmación de salida
  dias_confirmacion = 2,    # 2 días bajo MA para confirmar ruptura
  
  # Filtro de volatilidad
  volatilidad_max = 0.008
)

cat("📋 PARÁMETROS:\n")
for (param in names(PARAMETROS)) {
  valor <- PARAMETROS[[param]]
  if (is.numeric(valor) && valor < 1 && valor > 0) {
    cat(param, ":", valor * 100, "%\n")
  } else {
    cat(param, ":", valor, "\n")
  }
}
cat("\n")

# =============================================================================
# OBTENER Y PREPARAR DATOS
# =============================================================================

obtener_datos <- function() {
  cat("📥 Descargando datos EUR/USD...\n")
  
  fecha_fin <- Sys.Date()
  fecha_inicio <- fecha_fin - 1095  # 3 años
  
  tryCatch({
    datos <- getSymbols("EURUSD=X", 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos <- na.omit(datos)
    
    cat("✅ Datos obtenidos:", nrow(datos), "días\n\n")
    return(datos)
    
  }, error = function(e) {
    cat("❌ Error:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# CALCULAR INDICADORES (VERSIÓN SIMPLE)
# =============================================================================

calcular_indicadores <- function(datos) {
  cat("📊 Calculando indicadores...\n")
  
  precios <- Cl(datos)
  
  # Indicadores básicos
  ma_corta <- SMA(precios, n = PARAMETROS$ma_corta)
  ma_larga <- SMA(precios, n = PARAMETROS$ma_larga)
  rsi <- RSI(precios, n = PARAMETROS$rsi_periodo)
  volatilidad <- runSD(precios, n = 20)
  
  # Crear dataframe simple
  datos_completos <- data.frame(
    Fecha = index(datos),
    Precio = as.numeric(precios),
    MA_Corta = as.numeric(ma_corta),
    MA_Larga = as.numeric(ma_larga),
    RSI = as.numeric(rsi),
    Volatilidad = as.numeric(volatilidad)
  )
  
  # Eliminar NAs
  datos_completos <- datos_completos[complete.cases(datos_completos), ]
  
  cat("✅ Indicadores calculados:", nrow(datos_completos), "días válidos\n\n")
  return(datos_completos)
}

# =============================================================================
# ESTRATEGIA CON TENDENCIAS EXTENDIDAS
# =============================================================================

ejecutar_estrategia <- function(datos) {
  cat("🚀 Ejecutando estrategia de tendencia extendida...\n")
  
  if (is.null(datos) || nrow(datos) == 0) {
    return(data.frame())
  }
  
  # Variables de control
  posicion_abierta <- FALSE
  precio_entrada <- 0
  fecha_entrada <- NULL
  dias_en_posicion <- 0
  stop_loss <- 0
  take_profit <- 0
  operaciones <- data.frame()
  
  # Variable para confirmación de salida
  dias_consecutivos_bajo_ma <- 0
  
  for (i in 2:nrow(datos)) {
    fecha_actual <- datos$Fecha[i]
    precio_actual <- datos$Precio[i]
    ma_corta_actual <- datos$MA_Corta[i]
    ma_larga_actual <- datos$MA_Larga[i]
    ma_corta_anterior <- datos$MA_Corta[i-1]
    ma_larga_anterior <- datos$MA_Larga[i-1]
    rsi_actual <- datos$RSI[i]
    volatilidad_actual <- datos$Volatilidad[i]
    
    # Verificar datos válidos
    if (any(is.na(c(precio_actual, ma_corta_actual, ma_larga_actual, 
                    ma_corta_anterior, ma_larga_anterior, rsi_actual)))) {
      next
    }
    
    # ENTRADA
    if (!posicion_abierta) {
      # Condición 1: Cruce alcista de medias móviles
      cruce_alcista <- (ma_corta_anterior <= ma_larga_anterior) && 
                       (ma_corta_actual > ma_larga_actual)
      
      # Condición 2: RSI en rango favorable
      rsi_favorable <- rsi_actual >= PARAMETROS$rsi_entrada_min && 
                       rsi_actual <= PARAMETROS$rsi_entrada_max
      
      # Condición 3: Volatilidad normal
      volatilidad_ok <- is.na(volatilidad_actual) || volatilidad_actual <= PARAMETROS$volatilidad_max
      
      # ENTRADA
      if (cruce_alcista && rsi_favorable && volatilidad_ok) {
        posicion_abierta <- TRUE
        precio_entrada <- precio_actual
        fecha_entrada <- fecha_actual
        dias_en_posicion <- 0
        stop_loss <- precio_entrada * (1 - PARAMETROS$stop_loss_pct)
        take_profit <- precio_entrada * (1 + PARAMETROS$take_profit_pct)
        dias_consecutivos_bajo_ma <- 0
        
        cat("📈 ENTRADA:", as.character(fecha_actual), 
            "Precio:", round(precio_entrada, 5), 
            "RSI:", round(rsi_actual, 2), "\n")
      }
    }
    
    # SALIDA CON LÓGICA EXTENDIDA
    if (posicion_abierta) {
      dias_en_posicion <- dias_en_posicion + 1
      salida <- FALSE
      motivo_salida <- ""
      
      # Actualizar contador de días bajo MA
      if (precio_actual < ma_corta_actual) {
        dias_consecutivos_bajo_ma <- dias_consecutivos_bajo_ma + 1
      } else {
        dias_consecutivos_bajo_ma <- 0  # Reset si vuelve arriba
      }
      
      # Condición 1: RSI sobrecompra (solo después del tiempo mínimo)
      if (rsi_actual >= PARAMETROS$rsi_salida_max && 
          dias_en_posicion >= PARAMETROS$min_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "RSI_Sobrecompra"
      }
      
      # Condición 2: Ruptura CONFIRMADA (NUEVA LÓGICA)
      # Solo salir si está bajo MA por varios días consecutivos
      if (dias_consecutivos_bajo_ma >= PARAMETROS$dias_confirmacion && 
          dias_en_posicion >= PARAMETROS$min_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "Ruptura_Confirmada"
      }
      
      # Condición 3: Stop Loss
      if (precio_actual <= stop_loss) {
        salida <- TRUE
        motivo_salida <- "Stop_Loss"
      }
      
      # Condición 4: Take Profit
      if (precio_actual >= take_profit) {
        salida <- TRUE
        motivo_salida <- "Take_Profit"
      }
      
      # Condición 5: Tiempo máximo extendido
      if (dias_en_posicion >= PARAMETROS$max_dias_posicion) {
        salida <- TRUE
        motivo_salida <- "Tiempo_Maximo"
      }
      
      # EJECUTAR SALIDA
      if (salida) {
        precio_salida <- precio_actual
        rendimiento <- (precio_salida - precio_entrada) / precio_entrada
        rendimiento_pct <- rendimiento * 100
        
        # Calcular máxima ganancia potencial durante la operación
        indices_periodo <- which(datos$Fecha >= fecha_entrada & datos$Fecha <= fecha_actual)
        if (length(indices_periodo) > 0) {
          precios_periodo <- datos$Precio[indices_periodo]
          max_precio <- max(precios_periodo, na.rm = TRUE)
          max_ganancia_pct <- ((max_precio - precio_entrada) / precio_entrada) * 100
        } else {
          max_ganancia_pct <- rendimiento_pct
        }
        
        # Registrar operación
        nueva_operacion <- data.frame(
          Entrada_Fecha = fecha_entrada,
          Entrada_Precio = precio_entrada,
          Salida_Fecha = fecha_actual,
          Salida_Precio = precio_salida,
          Dias_Posicion = dias_en_posicion,
          Rendimiento_Pct = rendimiento_pct,
          Max_Ganancia_Pct = max_ganancia_pct,
          Motivo_Salida = motivo_salida,
          Ganadora = rendimiento > 0,
          Dias_Bajo_MA = dias_consecutivos_bajo_ma
        )
        
        operaciones <- rbind(operaciones, nueva_operacion)
        
        cat("📉 SALIDA:", as.character(fecha_actual), 
            "Precio:", round(precio_salida, 5), 
            "Días:", dias_en_posicion,
            "Rendimiento:", round(rendimiento_pct, 2), "%",
            "Max Potencial:", round(max_ganancia_pct, 2), "%",
            "Motivo:", motivo_salida, "\n")
        
        # Reset variables
        posicion_abierta <- FALSE
        precio_entrada <- 0
        fecha_entrada <- NULL
        dias_en_posicion <- 0
        dias_consecutivos_bajo_ma <- 0
      }
    }
  }
  
  cat("✅ Estrategia completada:", nrow(operaciones), "operaciones\n\n")
  return(operaciones)
}

# =============================================================================
# ANÁLISIS DE RESULTADOS
# =============================================================================

analizar_resultados <- function(resultados) {
  if (nrow(resultados) == 0) {
    cat("❌ No se generaron operaciones\n")
    return()
  }
  
  cat("🎉 RESULTADOS DE ESTRATEGIA DE TENDENCIA EXTENDIDA:\n")
  cat("===================================================\n")
  
  # Métricas principales
  total_ops <- nrow(resultados)
  ops_ganadoras <- sum(resultados$Ganadora)
  tasa_exito <- (ops_ganadoras / total_ops) * 100
  rendimiento_total <- sum(resultados$Rendimiento_Pct)
  rendimiento_promedio <- mean(resultados$Rendimiento_Pct)
  mejor_operacion <- max(resultados$Rendimiento_Pct)
  peor_operacion <- min(resultados$Rendimiento_Pct)
  dias_promedio <- mean(resultados$Dias_Posicion)
  
  cat("📊 MÉTRICAS PRINCIPALES:\n")
  cat("Total operaciones:", total_ops, "\n")
  cat("Operaciones ganadoras:", ops_ganadoras, "\n")
  cat("Tasa de éxito:", round(tasa_exito, 1), "%\n")
  cat("Rendimiento total:", round(rendimiento_total, 2), "%\n")
  cat("Rendimiento promedio:", round(rendimiento_promedio, 2), "%\n")
  cat("Mejor operación:", round(mejor_operacion, 2), "%\n")
  cat("Peor operación:", round(peor_operacion, 2), "%\n")
  cat("Días promedio en posición:", round(dias_promedio, 1), "\n")
  
  # Análisis de eficiencia
  if ("Max_Ganancia_Pct" %in% colnames(resultados)) {
    max_ganancia_promedio <- mean(resultados$Max_Ganancia_Pct)
    eficiencia_promedio <- mean(resultados$Rendimiento_Pct / resultados$Max_Ganancia_Pct, na.rm = TRUE) * 100
    cat("Máxima ganancia promedio:", round(max_ganancia_promedio, 2), "%\n")
    cat("Eficiencia de captura:", round(eficiencia_promedio, 1), "%\n")
  }
  
  # Motivos de salida
  cat("\n📋 MOTIVOS DE SALIDA:\n")
  motivos <- table(resultados$Motivo_Salida)
  for (motivo in names(motivos)) {
    cat(motivo, ":", motivos[motivo], "operaciones\n")
  }
  
  # Comparación con versión anterior
  cat("\n🎯 COMPARACIÓN:\n")
  cat("Versión Balanceada    : 5 ops, 20% éxito, -0.36% rendimiento\n")
  cat("Versión Tendencia Ext :", total_ops, "ops,", round(tasa_exito, 1), "% éxito,", round(rendimiento_total, 2), "% rendimiento\n")
  
  if (tasa_exito > 20) {
    cat("✅ MEJORA en tasa de éxito\n")
  } else {
    cat("⚠️ Tasa de éxito similar o menor\n")
  }
  
  if (rendimiento_total > -0.36) {
    cat("✅ MEJORA en rendimiento\n")
  } else {
    cat("❌ Rendimiento similar o peor\n")
  }
  
  # Evaluación general
  cat("\n🏆 EVALUACIÓN GENERAL:\n")
  if (tasa_exito >= 50 && rendimiento_total > 2) {
    cat("🎉 EXCELENTE: Estrategia muy exitosa\n")
  } else if (tasa_exito >= 40 && rendimiento_total > 0) {
    cat("✅ BUENA: Estrategia prometedora\n")
  } else if (rendimiento_total > -0.5) {
    cat("⚠️ ACEPTABLE: Necesita ajustes menores\n")
  } else {
    cat("❌ NECESITA MEJORAS: Revisar parámetros\n")
  }
  
  cat("\n📈 DETALLE DE OPERACIONES:\n")
  print(resultados[, c("Entrada_Fecha", "Salida_Fecha", "Dias_Posicion", 
                      "Rendimiento_Pct", "Max_Ganancia_Pct", "Motivo_Salida", "Ganadora")])
}

# =============================================================================
# EJECUCIÓN PRINCIPAL
# =============================================================================

# Ejecutar estrategia
datos <- obtener_datos()

if (!is.null(datos)) {
  datos_con_indicadores <- calcular_indicadores(datos)
  resultados <- ejecutar_estrategia(datos_con_indicadores)
  analizar_resultados(resultados)
} else {
  cat("❌ No se pudieron obtener datos\n")
}

cat("\n📈 ESTRATEGIA DE TENDENCIA EXTENDIDA COMPLETADA\n")
cat("===============================================\n")
cat("🔑 Innovaciones clave:\n")
cat("   - Tiempo mínimo: 5 días (vs 2 anterior)\n")
cat("   - Tiempo máximo: 25 días (vs 15 anterior)\n")
cat("   - Confirmación de ruptura: 2 días consecutivos\n")
cat("   - Take Profit más ambicioso: 4% (vs 3% anterior)\n")
