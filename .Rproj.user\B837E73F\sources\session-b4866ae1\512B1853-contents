# =============================================================================
# ESTRATEGIA DAY TRADING - 3 OPERACIONES DIARIAS MÁXIMO
# =============================================================================
# Estrategia agresiva para day trading con gestión estricta de capital
# Target: 60% ganancia | Stop: 20% pérdida | Máximo 3 ops/día
# =============================================================================

library(quantmod)
library(TTR)
library(dplyr)

cat("⚡ ESTRATEGIA DAY TRADING\n")
cat("========================\n")
cat("Objetivo: 3 operaciones máximo por día\n")
cat("Capital utilizable: 10% del total\n")
cat("Stop Loss: 20% | Take Profit: 60%\n\n")

# =============================================================================
# PARÁMETROS DE DAY TRADING
# =============================================================================

PARAMETROS_DAY <- list(
  # Gestión de capital (TUS REGLAS)
  capital_utilizable_pct = 0.10,    # 10% del capital total
  riesgo_por_operacion_pct = 0.02,  # 2% del capital total por operación
  stop_loss_pct = 0.20,             # 20% stop loss
  take_profit_pct = 0.60,           # 60% take profit
  max_operaciones_dia = 3,          # Máximo 3 operaciones por día
  
  # Indicadores técnicos para day trading
  ma_rapida = 8,                    # MA rápida (8 períodos)
  ma_lenta = 21,                    # MA lenta (21 períodos)
  rsi_periodo = 14,                 # RSI estándar
  rsi_sobrecompra = 70,             # RSI sobrecompra
  rsi_sobreventa = 30,              # RSI sobreventa
  rsi_neutral_alto = 55,            # RSI neutral alto
  rsi_neutral_bajo = 45,            # RSI neutral bajo
  
  # MACD para confirmación (ajustado para datos limitados)
  macd_fast = 5,                    # MACD línea rápida (reducido)
  macd_slow = 13,                   # MACD línea lenta (reducido)
  macd_signal = 5,                  # MACD señal (reducido)
  
  # Volumen para confirmación
  volumen_factor = 1.2,             # Volumen 20% arriba del promedio
  volumen_periodos = 20,            # Promedio de volumen
  
  # Filtros de tiempo
  hora_inicio = 8,                  # 8:00 AM
  hora_fin = 20,                    # 8:00 PM
  evitar_noticias = TRUE            # Evitar horarios de noticias
)

cat("📋 CONFIGURACIÓN DAY TRADING:\n")
cat("=============================\n")
cat("Capital utilizable:", PARAMETROS_DAY$capital_utilizable_pct * 100, "%\n")
cat("Riesgo por operación:", PARAMETROS_DAY$riesgo_por_operacion_pct * 100, "% del total\n")
cat("Stop Loss:", PARAMETROS_DAY$stop_loss_pct * 100, "%\n")
cat("Take Profit:", PARAMETROS_DAY$take_profit_pct * 100, "%\n")
cat("Máximo operaciones/día:", PARAMETROS_DAY$max_operaciones_dia, "\n")
cat("Timeframe recomendado: 15 minutos\n")
cat("Horario operativo:", PARAMETROS_DAY$hora_inicio, ":00 -", PARAMETROS_DAY$hora_fin, ":00\n\n")

# =============================================================================
# FUNCIÓN PARA OBTENER DATOS INTRADAY
# =============================================================================

obtener_datos_intraday <- function(simbolo = "EURUSD=X", dias = 60) {
  cat("📥 Descargando datos intraday de", simbolo, "...\n")

  tryCatch({
    # Para day trading necesitamos más datos para calcular indicadores
    fecha_fin <- Sys.Date()
    fecha_inicio <- fecha_fin - dias
    
    # Obtener datos diarios (Yahoo Finance no da intraday gratis)
    # En práctica real usarías datos de 15min de tu broker
    datos <- getSymbols(simbolo, 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) == 0) {
      cat("❌ No se obtuvieron datos\n")
      return(NULL)
    }
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos <- na.omit(datos)
    
    cat("✅ Datos obtenidos:", nrow(datos), "períodos\n")
    cat("⚠️ NOTA: En trading real usar datos de 15 minutos\n\n")
    
    return(datos)
    
  }, error = function(e) {
    cat("❌ Error:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# FUNCIÓN PARA CALCULAR INDICADORES DAY TRADING
# =============================================================================

calcular_indicadores_day <- function(datos) {
  cat("📊 Calculando indicadores para day trading...\n")

  precios <- Cl(datos)
  volumen <- Vo(datos)
  high <- Hi(datos)
  low <- Lo(datos)

  n_datos <- length(precios)
  cat("Datos disponibles:", n_datos, "períodos\n")

  # Verificar que tenemos suficientes datos
  if (n_datos < 30) {
    cat("⚠️ Pocos datos disponibles, ajustando parámetros...\n")
  }

  # Medias móviles rápidas para day trading
  ma_rapida <- SMA(precios, n = min(PARAMETROS_DAY$ma_rapida, n_datos - 5))
  ma_lenta <- SMA(precios, n = min(PARAMETROS_DAY$ma_lenta, n_datos - 3))

  # RSI para momentum
  rsi <- RSI(precios, n = min(PARAMETROS_DAY$rsi_periodo, n_datos - 2))

  # MACD para confirmación de tendencia (con verificación)
  macd_slow_adj <- min(PARAMETROS_DAY$macd_slow, n_datos - 2)
  macd_fast_adj <- min(PARAMETROS_DAY$macd_fast, macd_slow_adj - 1)
  macd_signal_adj <- min(PARAMETROS_DAY$macd_signal, n_datos - macd_slow_adj - 2)

  if (macd_slow_adj > macd_fast_adj && macd_signal_adj > 0) {
    macd_data <- MACD(precios,
                      nFast = macd_fast_adj,
                      nSlow = macd_slow_adj,
                      nSig = macd_signal_adj)
  } else {
    # Si no hay suficientes datos para MACD, usar valores NA
    macd_data <- list(
      macd = rep(NA, n_datos),
      signal = rep(NA, n_datos)
    )
  }

  # Volumen promedio
  vol_periodos <- min(PARAMETROS_DAY$volumen_periodos, n_datos - 2)
  volumen_promedio <- SMA(volumen, n = vol_periodos)

  # ATR para volatilidad
  atr_periodos <- min(14, n_datos - 2)
  atr <- ATR(HLC(datos), n = atr_periodos)$atr
  
  # Crear dataframe completo
  df <- data.frame(
    Fecha = index(datos),
    Precio = as.numeric(precios),
    High = as.numeric(high),
    Low = as.numeric(low),
    Volumen = as.numeric(volumen),
    MA_Rapida = as.numeric(ma_rapida),
    MA_Lenta = as.numeric(ma_lenta),
    RSI = as.numeric(rsi),
    MACD = as.numeric(macd_data$macd),
    MACD_Signal = as.numeric(macd_data$signal),
    Volumen_Promedio = as.numeric(volumen_promedio),
    ATR = as.numeric(atr)
  )
  
  # Eliminar NAs
  df <- df[complete.cases(df), ]
  
  cat("✅ Indicadores calculados:", nrow(df), "períodos válidos\n\n")
  return(df)
}

# =============================================================================
# FUNCIÓN PARA DETECTAR SEÑALES DAY TRADING
# =============================================================================

detectar_señales_day <- function(datos) {
  cat("🎯 Detectando señales de day trading...\n")
  
  if (nrow(datos) < 3) {
    cat("❌ Datos insuficientes\n")
    return(data.frame())
  }
  
  señales <- data.frame()
  
  for (i in 3:nrow(datos)) {
    actual <- datos[i, ]
    anterior <- datos[i-1, ]
    anterior2 <- datos[i-2, ]
    
    # Verificar datos válidos
    if (any(is.na(c(actual$Precio, actual$MA_Rapida, actual$MA_Lenta, 
                    actual$RSI, actual$MACD, actual$MACD_Signal)))) {
      next
    }
    
    # =============================================================================
    # SEÑALES DE COMPRA (LONG)
    # =============================================================================
    
    # Condición 1: Cruce alcista de medias móviles
    cruce_alcista <- (anterior$MA_Rapida <= anterior$MA_Lenta) && 
                     (actual$MA_Rapida > actual$MA_Lenta)
    
    # Condición 2: RSI en zona favorable (no sobrecomprado)
    rsi_favorable <- actual$RSI > PARAMETROS_DAY$rsi_neutral_bajo && 
                     actual$RSI < PARAMETROS_DAY$rsi_sobrecompra
    
    # Condición 3: MACD alcista (con verificación de NA)
    macd_alcista <- if (!is.na(actual$MACD) && !is.na(actual$MACD_Signal) && !is.na(anterior$MACD)) {
      actual$MACD > actual$MACD_Signal && actual$MACD > anterior$MACD
    } else {
      TRUE  # Si no hay MACD, no bloquear la señal
    }
    
    # Condición 4: Volumen elevado
    volumen_alto <- actual$Volumen > (actual$Volumen_Promedio * PARAMETROS_DAY$volumen_factor)
    
    # Condición 5: Precio arriba de MA lenta
    precio_arriba_ma <- actual$Precio > actual$MA_Lenta
    
    # SEÑAL DE COMPRA
    if (cruce_alcista && rsi_favorable && macd_alcista && precio_arriba_ma) {
      señal <- data.frame(
        Fecha = actual$Fecha,
        Tipo = "COMPRA",
        Precio = actual$Precio,
        RSI = actual$RSI,
        MACD = actual$MACD,
        Volumen_Factor = actual$Volumen / actual$Volumen_Promedio,
        Fuerza = ifelse(volumen_alto, "FUERTE", "NORMAL"),
        MA_Rapida = actual$MA_Rapida,
        MA_Lenta = actual$MA_Lenta
      )
      
      señales <- rbind(señales, señal)
    }
    
    # =============================================================================
    # SEÑALES DE VENTA (SHORT) - OPCIONAL
    # =============================================================================
    
    # Cruce bajista
    cruce_bajista <- (anterior$MA_Rapida >= anterior$MA_Lenta) && 
                     (actual$MA_Rapida < actual$MA_Lenta)
    
    # RSI en zona de sobreventa pero no extrema
    rsi_bajista <- actual$RSI < PARAMETROS_DAY$rsi_neutral_alto && 
                   actual$RSI > PARAMETROS_DAY$rsi_sobreventa
    
    # MACD bajista (con verificación de NA)
    macd_bajista <- if (!is.na(actual$MACD) && !is.na(actual$MACD_Signal) && !is.na(anterior$MACD)) {
      actual$MACD < actual$MACD_Signal && actual$MACD < anterior$MACD
    } else {
      TRUE  # Si no hay MACD, no bloquear la señal
    }
    
    # Precio debajo de MA lenta
    precio_debajo_ma <- actual$Precio < actual$MA_Lenta
    
    # SEÑAL DE VENTA (solo si quieres operar en corto)
    if (cruce_bajista && rsi_bajista && macd_bajista && precio_debajo_ma) {
      señal <- data.frame(
        Fecha = actual$Fecha,
        Tipo = "VENTA",
        Precio = actual$Precio,
        RSI = actual$RSI,
        MACD = actual$MACD,
        Volumen_Factor = actual$Volumen / actual$Volumen_Promedio,
        Fuerza = ifelse(volumen_alto, "FUERTE", "NORMAL"),
        MA_Rapida = actual$MA_Rapida,
        MA_Lenta = actual$MA_Lenta
      )
      
      señales <- rbind(señales, señal)
    }
  }
  
  cat("✅ Señales detectadas:", nrow(señales), "\n\n")
  return(señales)
}

# =============================================================================
# SIMULADOR DE DAY TRADING
# =============================================================================

simular_day_trading <- function(datos, señales, capital_total = 10000) {
  cat("🎮 Simulando day trading...\n")
  
  if (nrow(señales) == 0) {
    cat("❌ No hay señales para simular\n")
    return(data.frame())
  }
  
  # Configuración inicial
  capital_utilizable <- capital_total * PARAMETROS_DAY$capital_utilizable_pct
  riesgo_por_op <- capital_total * PARAMETROS_DAY$riesgo_por_operacion_pct
  
  cat("💰 Capital total:", capital_total, "€\n")
  cat("💰 Capital utilizable (10%):", capital_utilizable, "€\n")
  cat("💰 Riesgo por operación (2%):", riesgo_por_op, "€\n\n")
  
  operaciones <- data.frame()
  operaciones_dia <- 0
  fecha_actual <- as.Date(señales$Fecha[1])
  ganancia_dia <- 0
  target_diario <- riesgo_por_op * 1.5  # Target conservador
  
  for (i in 1:nrow(señales)) {
    señal <- señales[i, ]
    fecha_señal <- as.Date(señal$Fecha)
    
    # Reset contador si es nuevo día
    if (fecha_señal != fecha_actual) {
      fecha_actual <- fecha_señal
      operaciones_dia <- 0
      ganancia_dia <- 0
    }
    
    # Verificar límites diarios
    if (operaciones_dia >= PARAMETROS_DAY$max_operaciones_dia) {
      next  # Ya se alcanzó el máximo de operaciones del día
    }
    
    if (ganancia_dia >= target_diario) {
      next  # Ya se alcanzó el target del día
    }
    
    # Calcular tamaño de posición
    precio_entrada <- señal$Precio
    stop_loss <- precio_entrada * (1 - PARAMETROS_DAY$stop_loss_pct)
    take_profit <- precio_entrada * (1 + PARAMETROS_DAY$take_profit_pct)
    
    # Tamaño basado en riesgo
    riesgo_por_pip <- precio_entrada - stop_loss
    tamaño_posicion <- riesgo_por_op / riesgo_por_pip
    
    # Simular resultado (simplificado)
    # En realidad necesitarías datos intraday para simular correctamente
    probabilidad_exito <- ifelse(señal$Fuerza == "FUERTE", 0.6, 0.45)
    exito <- runif(1) < probabilidad_exito
    
    if (exito) {
      resultado_pct <- PARAMETROS_DAY$take_profit_pct
      resultado_euros <- tamaño_posicion * (take_profit - precio_entrada)
    } else {
      resultado_pct <- -PARAMETROS_DAY$stop_loss_pct
      resultado_euros <- tamaño_posicion * (stop_loss - precio_entrada)
    }
    
    # Registrar operación
    operacion <- data.frame(
      Fecha = señal$Fecha,
      Tipo = señal$Tipo,
      Precio_Entrada = precio_entrada,
      Stop_Loss = stop_loss,
      Take_Profit = take_profit,
      Tamaño = tamaño_posicion,
      Resultado_Pct = resultado_pct * 100,
      Resultado_Euros = resultado_euros,
      Exito = exito,
      Fuerza_Señal = señal$Fuerza,
      RSI = señal$RSI,
      Operacion_Dia = operaciones_dia + 1
    )
    
    operaciones <- rbind(operaciones, operacion)
    operaciones_dia <- operaciones_dia + 1
    ganancia_dia <- ganancia_dia + resultado_euros
    
    cat("📈 Op", operaciones_dia, "-", señal$Tipo, ":", 
        round(precio_entrada, 5), "→", 
        ifelse(exito, "✅", "❌"), 
        round(resultado_pct * 100, 1), "%",
        "(", round(resultado_euros, 2), "€)\n")
  }
  
  cat("\n✅ Simulación completada:", nrow(operaciones), "operaciones\n\n")
  return(operaciones)
}

# =============================================================================
# ANÁLISIS DE RESULTADOS DAY TRADING
# =============================================================================

analizar_day_trading <- function(operaciones) {
  if (nrow(operaciones) == 0) {
    cat("❌ No hay operaciones para analizar\n")
    return()
  }
  
  cat("📊 ANÁLISIS DE DAY TRADING\n")
  cat("==========================\n")
  
  # Métricas básicas
  total_ops <- nrow(operaciones)
  ops_exitosas <- sum(operaciones$Exito)
  tasa_exito <- (ops_exitosas / total_ops) * 100
  
  ganancia_total <- sum(operaciones$Resultado_Euros)
  ganancia_promedio <- mean(operaciones$Resultado_Euros)
  mejor_operacion <- max(operaciones$Resultado_Euros)
  peor_operacion <- min(operaciones$Resultado_Euros)
  
  cat("Total operaciones:", total_ops, "\n")
  cat("Operaciones exitosas:", ops_exitosas, "\n")
  cat("Tasa de éxito:", round(tasa_exito, 1), "%\n")
  cat("Ganancia total:", round(ganancia_total, 2), "€\n")
  cat("Ganancia promedio:", round(ganancia_promedio, 2), "€\n")
  cat("Mejor operación:", round(mejor_operacion, 2), "€\n")
  cat("Peor operación:", round(peor_operacion, 2), "€\n")
  
  # Análisis por día
  operaciones$Fecha_Solo <- as.Date(operaciones$Fecha)
  dias_trading <- length(unique(operaciones$Fecha_Solo))
  ops_por_dia <- total_ops / dias_trading
  
  cat("\nDías de trading:", dias_trading, "\n")
  cat("Operaciones por día:", round(ops_por_dia, 1), "\n")
  
  # Análisis de riesgo
  max_perdida <- min(operaciones$Resultado_Euros)
  drawdown <- abs(max_perdida)
  
  cat("\nMáxima pérdida individual:", round(max_perdida, 2), "€\n")
  cat("Drawdown máximo:", round(drawdown, 2), "€\n")
  
  # Evaluación
  cat("\n🎯 EVALUACIÓN:\n")
  if (tasa_exito >= 60 && ganancia_total > 0) {
    cat("🎉 EXCELENTE: Estrategia muy rentable\n")
  } else if (tasa_exito >= 50 && ganancia_total > 0) {
    cat("✅ BUENA: Estrategia rentable\n")
  } else if (ganancia_total > 0) {
    cat("⚠️ ACEPTABLE: Rentable pero mejorable\n")
  } else {
    cat("❌ NECESITA MEJORAS: Pérdidas\n")
  }
  
  cat("\n📋 DETALLE DE OPERACIONES:\n")
  print(operaciones[, c("Fecha", "Tipo", "Precio_Entrada", "Resultado_Pct", 
                        "Resultado_Euros", "Exito", "Operacion_Dia")])
}

# =============================================================================
# EJECUCIÓN PRINCIPAL
# =============================================================================

cat("🎯 Iniciando análisis de day trading...\n\n")

# Obtener datos (más días para tener suficientes datos)
datos_intraday <- obtener_datos_intraday("EURUSD=X", 90)

if (!is.null(datos_intraday)) {
  # Calcular indicadores
  datos_con_indicadores <- calcular_indicadores_day(datos_intraday)
  
  # Detectar señales
  señales_day <- detectar_señales_day(datos_con_indicadores)
  
  # Simular trading
  if (nrow(señales_day) > 0) {
    operaciones_day <- simular_day_trading(datos_con_indicadores, señales_day, 10000)
    analizar_day_trading(operaciones_day)
  } else {
    cat("⏸️ No se detectaron señales de day trading\n")
  }
} else {
  cat("❌ No se pudieron obtener datos\n")
}

cat("\n⚡ DAY TRADING STRATEGY COMPLETADA\n")
cat("==================================\n")
cat("💡 Para trading real: usar datos de 15 minutos\n")
cat("🎯 Recordar: Máximo 3 operaciones por día\n")
cat("🛡️ Stop Loss: 20% | Take Profit: 60%\n")
