# =============================================================================
# ESTRATEGIA DE TRADING PARA ORO (XAU/USD)
# =============================================================================
# Estrategia específica para oro con gestión de riesgo adaptada
# Enfoque en breakouts y seguimiento de tendencia
# =============================================================================

library(quantmod)
library(TTR)

cat("🥇 ESTRATEGIA DE TRADING PARA ORO\n")
cat("==================================\n")
cat("Mercado: XAU/USD (Oro vs Dólar)\n\n")

# =============================================================================
# PARÁMETROS ESPECÍFICOS PARA ORO
# =============================================================================

PARAMETROS_ORO <- list(
  # Gestión de capital para oro (más conservadora)
  capital_total = 10000,
  capital_utilizable_pct = 0.08,       # 8% (menos que forex por mayor volatilidad)
  riesgo_por_operacion_pct = 0.015,    # 1.5% por operación
  
  # Gestión de riesgo específica para oro
  stop_loss_pct = 0.02,                # 2% stop loss
  take_profit_pct = 0.04,              # 4% take profit
  trailing_stop_pct = 0.015,           # 1.5% trailing stop
  
  # Indicadores técnicos para oro
  ma_corta = 20,                       # MA corta
  ma_larga = 50,                       # MA larga
  ma_tendencia = 200,                  # MA de tendencia principal
  
  # RSI específico para oro
  rsi_periodo = 14,
  rsi_sobrecompra = 75,                # Oro puede estar más tiempo sobrecomprado
  rsi_sobreventa = 25,                 # Oro puede estar más tiempo sobrevendido
  
  # Bollinger Bands para breakouts
  bb_periodo = 20,
  bb_desviaciones = 2,
  
  # ATR para volatilidad
  atr_periodo = 14,
  atr_multiplicador = 2,               # Para stops dinámicos
  
  # Filtros específicos para oro
  volumen_factor = 1.5,                # Volumen 50% arriba del promedio
  volatilidad_max = 0.03,              # 3% volatilidad máxima diaria
  
  # Horarios de trading (UTC)
  hora_inicio = 1,                     # 01:00 UTC (apertura asiática)
  hora_fin = 21,                       # 21:00 UTC (cierre US)
  
  # Filtros fundamentales
  evitar_fomc = TRUE,                  # Evitar días de Fed
  evitar_nfp = TRUE                    # Evitar días de NFP
)

cat("📋 CONFIGURACIÓN PARA ORO:\n")
cat("==========================\n")
cat("Capital utilizable:", PARAMETROS_ORO$capital_total * PARAMETROS_ORO$capital_utilizable_pct, "€\n")
cat("Riesgo por operación:", PARAMETROS_ORO$capital_total * PARAMETROS_ORO$riesgo_por_operacion_pct, "€\n")
cat("Stop Loss:", PARAMETROS_ORO$stop_loss_pct * 100, "%\n")
cat("Take Profit:", PARAMETROS_ORO$take_profit_pct * 100, "%\n")
cat("Trailing Stop:", PARAMETROS_ORO$trailing_stop_pct * 100, "%\n\n")

# =============================================================================
# FUNCIÓN PARA OBTENER DATOS DE ORO
# =============================================================================

obtener_datos_oro <- function(dias = 365) {
  cat("📥 Descargando datos de oro (XAU/USD)...\n")
  
  tryCatch({
    fecha_fin <- Sys.Date()
    fecha_inicio <- fecha_fin - dias
    
    # Obtener datos de oro
    datos_oro <- getSymbols("GC=F",  # Gold Futures
                           src = "yahoo",
                           from = fecha_inicio,
                           to = fecha_fin,
                           auto.assign = FALSE,
                           warnings = FALSE)
    
    if (is.null(datos_oro) || nrow(datos_oro) == 0) {
      # Intentar con símbolo alternativo
      datos_oro <- getSymbols("XAUUSD=X",
                             src = "yahoo", 
                             from = fecha_inicio,
                             to = fecha_fin,
                             auto.assign = FALSE,
                             warnings = FALSE)
    }
    
    if (is.null(datos_oro) || nrow(datos_oro) == 0) {
      cat("❌ No se pudieron obtener datos de oro\n")
      return(NULL)
    }
    
    colnames(datos_oro) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos_oro <- na.omit(datos_oro)
    
    cat("✅ Datos de oro obtenidos:", nrow(datos_oro), "días\n")
    cat("Precio actual:", round(as.numeric(Cl(datos_oro)[nrow(datos_oro)]), 2), "USD\n\n")
    
    return(datos_oro)
    
  }, error = function(e) {
    cat("❌ Error obteniendo datos de oro:", e$message, "\n")
    return(NULL)
  })
}

# =============================================================================
# FUNCIÓN PARA CALCULAR INDICADORES ESPECÍFICOS PARA ORO
# =============================================================================

calcular_indicadores_oro <- function(datos) {
  cat("📊 Calculando indicadores específicos para oro...\n")
  
  precios <- Cl(datos)
  high <- Hi(datos)
  low <- Lo(datos)
  volumen <- Vo(datos)
  
  # Medias móviles
  ma_corta <- SMA(precios, n = PARAMETROS_ORO$ma_corta)
  ma_larga <- SMA(precios, n = PARAMETROS_ORO$ma_larga)
  ma_tendencia <- SMA(precios, n = PARAMETROS_ORO$ma_tendencia)
  
  # RSI
  rsi <- RSI(precios, n = PARAMETROS_ORO$rsi_periodo)
  
  # Bollinger Bands para breakouts
  bb <- BBands(precios, n = PARAMETROS_ORO$bb_periodo, sd = PARAMETROS_ORO$bb_desviaciones)
  
  # ATR para volatilidad
  atr <- ATR(HLC(datos), n = PARAMETROS_ORO$atr_periodo)
  
  # MACD para momentum
  macd <- MACD(precios, nFast = 12, nSlow = 26, nSig = 9)
  
  # Volumen promedio
  volumen_promedio <- SMA(volumen, n = 20)
  
  # Volatilidad diaria
  volatilidad <- runSD(ROC(precios), n = 20)
  
  # Crear dataframe completo
  df <- data.frame(
    Fecha = index(datos),
    Precio = as.numeric(precios),
    High = as.numeric(high),
    Low = as.numeric(low),
    Volumen = as.numeric(volumen),
    MA_Corta = as.numeric(ma_corta),
    MA_Larga = as.numeric(ma_larga),
    MA_Tendencia = as.numeric(ma_tendencia),
    RSI = as.numeric(rsi),
    BB_Upper = as.numeric(bb$up),
    BB_Middle = as.numeric(bb$mavg),
    BB_Lower = as.numeric(bb$dn),
    ATR = as.numeric(atr$atr),
    MACD = as.numeric(macd$macd),
    MACD_Signal = as.numeric(macd$signal),
    Volumen_Promedio = as.numeric(volumen_promedio),
    Volatilidad = as.numeric(volatilidad)
  )
  
  df <- df[complete.cases(df), ]
  
  cat("✅ Indicadores calculados:", nrow(df), "días válidos\n\n")
  return(df)
}

# =============================================================================
# FUNCIÓN PARA DETECTAR SEÑALES DE ORO
# =============================================================================

detectar_señales_oro <- function(datos) {
  cat("🎯 Detectando señales de trading para oro...\n")
  
  señales <- data.frame()
  
  for (i in 3:nrow(datos)) {
    actual <- datos[i, ]
    anterior <- datos[i-1, ]
    anterior2 <- datos[i-2, ]
    
    # Verificar datos válidos
    if (any(is.na(c(actual$Precio, actual$MA_Corta, actual$MA_Larga, 
                    actual$RSI, actual$BB_Upper, actual$BB_Lower)))) {
      next
    }
    
    # =============================================================================
    # ESTRATEGIA 1: BREAKOUT DE BOLLINGER BANDS
    # =============================================================================
    
    # Breakout alcista
    breakout_alcista <- (anterior$Precio <= anterior$BB_Upper) && 
                        (actual$Precio > actual$BB_Upper)
    
    # Confirmación con volumen
    volumen_alto <- actual$Volumen > (actual$Volumen_Promedio * PARAMETROS_ORO$volumen_factor)
    
    # RSI no sobrecomprado extremo
    rsi_ok_compra <- actual$RSI < PARAMETROS_ORO$rsi_sobrecompra
    
    # Tendencia principal alcista
    tendencia_alcista <- actual$Precio > actual$MA_Tendencia
    
    # SEÑAL DE COMPRA - BREAKOUT
    if (breakout_alcista && volumen_alto && rsi_ok_compra && tendencia_alcista) {
      señal <- data.frame(
        Fecha = actual$Fecha,
        Tipo = "COMPRA",
        Estrategia = "Breakout_Alcista",
        Precio = actual$Precio,
        RSI = actual$RSI,
        ATR = actual$ATR,
        Volumen_Factor = actual$Volumen / actual$Volumen_Promedio,
        Fuerza = "ALTA",
        BB_Position = "Arriba_BB_Superior"
      )
      
      señales <- rbind(señales, señal)
    }
    
    # =============================================================================
    # ESTRATEGIA 2: SEGUIMIENTO DE TENDENCIA
    # =============================================================================
    
    # Cruce alcista de medias
    cruce_alcista <- (anterior$MA_Corta <= anterior$MA_Larga) && 
                     (actual$MA_Corta > actual$MA_Larga)
    
    # MACD alcista
    macd_alcista <- actual$MACD > actual$MACD_Signal && 
                    actual$MACD > anterior$MACD
    
    # RSI en zona favorable
    rsi_favorable <- actual$RSI > 30 && actual$RSI < 70
    
    # Precio arriba de MA de tendencia
    precio_arriba_tendencia <- actual$Precio > actual$MA_Tendencia
    
    # SEÑAL DE COMPRA - TENDENCIA
    if (cruce_alcista && macd_alcista && rsi_favorable && precio_arriba_tendencia) {
      señal <- data.frame(
        Fecha = actual$Fecha,
        Tipo = "COMPRA",
        Estrategia = "Seguimiento_Tendencia",
        Precio = actual$Precio,
        RSI = actual$RSI,
        ATR = actual$ATR,
        Volumen_Factor = actual$Volumen / actual$Volumen_Promedio,
        Fuerza = "MEDIA",
        BB_Position = "Dentro_Bandas"
      )
      
      señales <- rbind(señales, señal)
    }
    
    # =============================================================================
    # ESTRATEGIA 3: REVERSIÓN EN SOPORTE
    # =============================================================================
    
    # Precio cerca del soporte (BB inferior)
    cerca_soporte <- actual$Precio <= (actual$BB_Lower * 1.01)
    
    # RSI sobreventa
    rsi_sobreventa <- actual$RSI <= PARAMETROS_ORO$rsi_sobreventa
    
    # Señal de reversión (precio sube desde soporte)
    reversi_alcista <- (anterior$Precio <= anterior$BB_Lower) && 
                       (actual$Precio > anterior$Precio)
    
    # Tendencia principal sigue alcista
    tendencia_principal_ok <- actual$MA_Tendencia > datos$MA_Tendencia[max(1, i-10)]
    
    # SEÑAL DE COMPRA - REVERSIÓN
    if (cerca_soporte && rsi_sobreventa && reversi_alcista && tendencia_principal_ok) {
      señal <- data.frame(
        Fecha = actual$Fecha,
        Tipo = "COMPRA",
        Estrategia = "Reversi_Soporte",
        Precio = actual$Precio,
        RSI = actual$RSI,
        ATR = actual$ATR,
        Volumen_Factor = actual$Volumen / actual$Volumen_Promedio,
        Fuerza = "MEDIA",
        BB_Position = "Cerca_BB_Inferior"
      )
      
      señales <- rbind(señales, señal)
    }
  }
  
  cat("✅ Señales detectadas:", nrow(señales), "\n\n")
  return(señales)
}

# =============================================================================
# FUNCIÓN PARA CALCULAR NIVELES DE TRADING PARA ORO
# =============================================================================

calcular_niveles_oro <- function(precio_entrada, atr_actual) {
  # Stop loss basado en ATR (más dinámico)
  stop_loss_atr <- precio_entrada - (atr_actual * PARAMETROS_ORO$atr_multiplicador)
  stop_loss_pct <- precio_entrada * (1 - PARAMETROS_ORO$stop_loss_pct)
  
  # Usar el stop loss más conservador
  stop_loss <- max(stop_loss_atr, stop_loss_pct)
  
  # Take profit
  take_profit <- precio_entrada * (1 + PARAMETROS_ORO$take_profit_pct)
  
  # Calcular tamaño de posición
  riesgo_euros <- PARAMETROS_ORO$capital_total * PARAMETROS_ORO$riesgo_por_operacion_pct
  riesgo_puntos <- precio_entrada - stop_loss
  
  # Para oro: 1 lote = 100 onzas, cada punto = $100
  if (riesgo_puntos > 0) {
    tamaño_lotes <- riesgo_euros / (riesgo_puntos * 100)
    tamaño_lotes <- round(tamaño_lotes, 2)
    tamaño_lotes <- max(0.01, tamaño_lotes)
  } else {
    tamaño_lotes <- 0.01
  }
  
  ganancia_potencial <- tamaño_lotes * (take_profit - precio_entrada) * 100
  
  return(list(
    precio_entrada = precio_entrada,
    stop_loss = stop_loss,
    take_profit = take_profit,
    tamaño_lotes = tamaño_lotes,
    riesgo_euros = riesgo_euros,
    ganancia_potencial = ganancia_potencial,
    ratio_riesgo_beneficio = (take_profit - precio_entrada) / (precio_entrada - stop_loss)
  ))
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE ANÁLISIS DE ORO
# =============================================================================

analizar_oro <- function() {
  cat("🥇 INICIANDO ANÁLISIS DE ORO\n")
  cat("============================\n\n")
  
  # Obtener datos
  datos_oro <- obtener_datos_oro(365)
  
  if (is.null(datos_oro)) {
    cat("❌ No se pueden analizar datos de oro\n")
    return()
  }
  
  # Calcular indicadores
  datos_con_indicadores <- calcular_indicadores_oro(datos_oro)
  
  # Detectar señales
  señales_oro <- detectar_señales_oro(datos_con_indicadores)
  
  # Mostrar resultados
  mostrar_resultados_oro(señales_oro, datos_con_indicadores)
}

# =============================================================================
# FUNCIÓN PARA MOSTRAR RESULTADOS
# =============================================================================

mostrar_resultados_oro <- function(señales, datos) {
  if (nrow(señales) == 0) {
    cat("⏸️ No hay señales de oro actualmente\n")
    cat("💡 El oro requiere paciencia - las mejores oportunidades son escasas\n")
    return()
  }
  
  cat("🎉 SEÑALES DE ORO DETECTADAS\n")
  cat("============================\n")
  
  # Mostrar últimas señales
  señales_recientes <- tail(señales, 3)
  
  for (i in 1:nrow(señales_recientes)) {
    señal <- señales_recientes[i, ]
    
    # Obtener ATR actual para cálculos
    atr_actual <- datos$ATR[nrow(datos)]
    niveles <- calcular_niveles_oro(señal$Precio, atr_actual)
    
    cat("📈", señal$Estrategia, "-", señal$Tipo, "\n")
    cat("   Fecha:", as.character(señal$Fecha), "\n")
    cat("   Precio:", round(señal$Precio, 2), "USD\n")
    cat("   RSI:", round(señal$RSI, 2), "\n")
    cat("   Fuerza:", señal$Fuerza, "\n")
    
    cat("   💰 NIVELES PARA ORO:\n")
    cat("      Tamaño:", niveles$tamaño_lotes, "lotes\n")
    cat("      Stop Loss:", round(niveles$stop_loss, 2), "USD\n")
    cat("      Take Profit:", round(niveles$take_profit, 2), "USD\n")
    cat("      Riesgo:", round(niveles$riesgo_euros, 2), "€\n")
    cat("      Ganancia potencial:", round(niveles$ganancia_potencial, 2), "€\n")
    cat("      Ratio R:B:", round(niveles$ratio_riesgo_beneficio, 2), ":1\n\n")
  }
  
  cat("📋 CONSIDERACIONES ESPECIALES PARA ORO:\n")
  cat("□ Verificar noticias de la Fed antes de operar\n")
  cat("□ Revisar datos de inflación (CPI, PPI)\n")
  cat("□ Considerar eventos geopolíticos\n")
  cat("□ Usar trailing stops por la volatilidad\n")
  cat("□ Monitorear correlación con USD\n")
}

# =============================================================================
# EJECUCIÓN PRINCIPAL
# =============================================================================

analizar_oro()

cat("\n🥇 ANÁLISIS DE ORO COMPLETADO\n")
cat("=============================\n")
cat("💡 El oro requiere gestión de riesgo estricta\n")
cat("📈 Mejores horarios: 13:30-15:30 UTC (overlap London-NY)\n")
cat("⚠️ Evitar trading durante anuncios de la Fed\n")
