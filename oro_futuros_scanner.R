# =============================================================================
# SCANNER INTEGRADO: ORO Y FUTUROS
# =============================================================================
# Análisis combinado de oportunidades en oro y futuros
# Comparación de señales y recomendaciones de trading
# =============================================================================

library(quantmod)
library(TTR)

cat("🥇📈 SCANNER ORO Y FUTUROS\n")
cat("==========================\n")
cat("Análisis integrado de oportunidades\n\n")

# =============================================================================
# CONFIGURACIÓN INTEGRADA
# =============================================================================

CONFIG_INTEGRADO <- list(
  # Capital y riesgo
  capital_total = 25000,               # Capital mínimo recomendado
  riesgo_maximo_dia = 0.05,            # 5% riesgo máximo por día
  max_posiciones_simultaneas = 3,      # Máximo 3 posiciones abiertas
  
  # Mercados a analizar
  mercados = list(
    # Oro
    "GOLD" = list(
      simbolo = "GC=F",
      nombre = "Oro",
      tipo = "commodity",
      valor_punto = 100,
      margen_aprox = 8000,
      volatilidad_esperada = 0.02,
      correlacion_usd = -0.7
    ),
    
    # Índices
    "SP500" = list(
      simbolo = "ES=F",
      nombre = "S&P 500",
      tipo = "indice",
      valor_punto = 50,
      margen_aprox = 13000,
      volatilidad_esperada = 0.015,
      correlacion_usd = -0.3
    ),
    
    "NASDAQ" = list(
      simbolo = "NQ=F",
      nombre = "Nasdaq 100",
      tipo = "indice",
      valor_punto = 20,
      margen_aprox = 17000,
      volatilidad_esperada = 0.02,
      correlacion_usd = -0.2
    ),
    
    # Petróleo
    "OIL" = list(
      simbolo = "CL=F",
      nombre = "Petróleo",
      tipo = "commodity",
      valor_punto = 1000,
      margen_aprox = 6000,
      volatilidad_esperada = 0.03,
      correlacion_usd = -0.4
    )
  ),
  
  # Filtros de calidad
  rsi_min = 30,
  rsi_max = 75,
  volumen_factor_min = 1.2,
  atr_factor_max = 2.0
)

# =============================================================================
# FUNCIÓN PARA ANÁLISIS RÁPIDO DE MERCADO
# =============================================================================

analisis_rapido_mercado <- function(mercado_info, nombre_mercado) {
  cat("🔍", mercado_info$nombre, "...")
  
  tryCatch({
    # Obtener datos recientes
    fecha_fin <- Sys.Date()
    fecha_inicio <- fecha_fin - 60
    
    datos <- getSymbols(mercado_info$simbolo,
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE,
                       warnings = FALSE)
    
    if (is.null(datos) || nrow(datos) < 30) {
      cat(" ❌ Sin datos\n")
      return(NULL)
    }
    
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    datos <- na.omit(datos)
    
    # Calcular indicadores básicos
    precios <- Cl(datos)
    ma_corta <- SMA(precios, n = 10)
    ma_larga <- SMA(precios, n = 20)
    rsi <- RSI(precios, n = 14)
    atr <- ATR(HLC(datos), n = 14)
    volumen <- Vo(datos)
    volumen_promedio <- SMA(volumen, n = 20)
    
    # Datos actuales
    n <- nrow(datos)
    precio_actual <- as.numeric(precios[n])
    ma_corta_actual <- as.numeric(ma_corta[n])
    ma_larga_actual <- as.numeric(ma_larga[n])
    rsi_actual <- as.numeric(rsi[n])
    atr_actual <- as.numeric(atr$atr[n])
    volumen_actual <- as.numeric(volumen[n])
    volumen_prom_actual <- as.numeric(volumen_promedio[n])
    
    # Evaluar señal
    señal <- evaluar_señal_mercado(precio_actual, ma_corta_actual, ma_larga_actual, 
                                  rsi_actual, volumen_actual, volumen_prom_actual,
                                  mercado_info)
    
    cat(" ✅", señal$calidad, "\n")
    
    return(list(
      mercado = nombre_mercado,
      nombre = mercado_info$nombre,
      tipo = mercado_info$tipo,
      precio = precio_actual,
      ma_corta = ma_corta_actual,
      ma_larga = ma_larga_actual,
      rsi = rsi_actual,
      atr = atr_actual,
      volumen_factor = volumen_actual / volumen_prom_actual,
      señal = señal,
      info = mercado_info
    ))
    
  }, error = function(e) {
    cat(" ❌ Error\n")
    return(NULL)
  })
}

# =============================================================================
# FUNCIÓN PARA EVALUAR SEÑAL DE MERCADO
# =============================================================================

evaluar_señal_mercado <- function(precio, ma_corta, ma_larga, rsi, volumen, volumen_prom, mercado_info) {
  
  # Verificar datos válidos
  if (any(is.na(c(precio, ma_corta, ma_larga, rsi)))) {
    return(list(tipo = "NINGUNA", calidad = "SIN_DATOS", fuerza = 0))
  }
  
  # Evaluar condiciones
  tendencia_alcista <- ma_corta > ma_larga
  precio_arriba_ma <- precio > ma_larga
  rsi_favorable <- rsi >= CONFIG_INTEGRADO$rsi_min && rsi <= CONFIG_INTEGRADO$rsi_max
  volumen_alto <- (volumen / volumen_prom) >= CONFIG_INTEGRADO$volumen_factor_min
  
  # Determinar tipo de señal
  if (tendencia_alcista && precio_arriba_ma && rsi_favorable) {
    if (volumen_alto) {
      return(list(tipo = "COMPRA", calidad = "ALTA", fuerza = 5))
    } else {
      return(list(tipo = "COMPRA", calidad = "MEDIA", fuerza = 3))
    }
  } else if (tendencia_alcista && precio_arriba_ma) {
    return(list(tipo = "COMPRA", calidad = "BAJA", fuerza = 2))
  } else if (!tendencia_alcista && precio < ma_larga && rsi < 30) {
    return(list(tipo = "VENTA", calidad = "MEDIA", fuerza = 3))
  } else {
    return(list(tipo = "ESPERAR", calidad = "NEUTRAL", fuerza = 1))
  }
}

# =============================================================================
# FUNCIÓN PARA CALCULAR NIVELES INTEGRADOS
# =============================================================================

calcular_niveles_integrados <- function(resultado) {
  precio_entrada <- resultado$precio
  mercado_info <- resultado$info
  
  # Calcular stop loss y take profit según tipo de mercado
  if (mercado_info$tipo == "commodity") {
    stop_loss_pct <- 0.02  # 2% para commodities
    take_profit_pct <- 0.04  # 4% para commodities
  } else {
    # Para índices usar puntos fijos aproximados
    if (grepl("SP500", resultado$mercado)) {
      stop_loss <- precio_entrada - 15  # 15 puntos
      take_profit <- precio_entrada + 30  # 30 puntos
    } else if (grepl("NASDAQ", resultado$mercado)) {
      stop_loss <- precio_entrada - 50  # 50 puntos
      take_profit <- precio_entrada + 100  # 100 puntos
    } else {
      stop_loss_pct <- 0.015
      take_profit_pct <- 0.03
    }
  }
  
  # Si usamos porcentajes
  if (exists("stop_loss_pct")) {
    stop_loss <- precio_entrada * (1 - stop_loss_pct)
    take_profit <- precio_entrada * (1 + take_profit_pct)
  }
  
  # Calcular tamaño de posición
  riesgo_euros <- CONFIG_INTEGRADO$capital_total * 0.02  # 2% por operación
  riesgo_puntos <- precio_entrada - stop_loss
  valor_por_punto <- mercado_info$valor_punto
  
  if (riesgo_puntos > 0) {
    num_contratos <- floor(riesgo_euros / (riesgo_puntos * valor_por_punto))
    num_contratos <- max(1, num_contratos)
  } else {
    num_contratos <- 1
  }
  
  ganancia_potencial <- num_contratos * (take_profit - precio_entrada) * valor_por_punto
  riesgo_real <- num_contratos * (precio_entrada - stop_loss) * valor_por_punto
  margen_requerido <- num_contratos * mercado_info$margen_aprox
  
  return(list(
    precio_entrada = precio_entrada,
    stop_loss = stop_loss,
    take_profit = take_profit,
    num_contratos = num_contratos,
    riesgo_euros = riesgo_real,
    ganancia_potencial = ganancia_potencial,
    margen_requerido = margen_requerido,
    ratio_rb = ganancia_potencial / riesgo_real
  ))
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE ESCANEO
# =============================================================================

escanear_oro_futuros <- function() {
  cat("🎯 ESCANEANDO ORO Y FUTUROS\n")
  cat("===========================\n\n")
  
  resultados <- list()
  
  # Analizar cada mercado
  for (nombre_mercado in names(CONFIG_INTEGRADO$mercados)) {
    mercado_info <- CONFIG_INTEGRADO$mercados[[nombre_mercado]]
    resultado <- analisis_rapido_mercado(mercado_info, nombre_mercado)
    
    if (!is.null(resultado)) {
      resultados[[length(resultados) + 1]] <- resultado
    }
    
    Sys.sleep(0.3)
  }
  
  # Mostrar resultados
  mostrar_resultados_integrados(resultados)
}

# =============================================================================
# FUNCIÓN PARA MOSTRAR RESULTADOS INTEGRADOS
# =============================================================================

mostrar_resultados_integrados <- function(resultados) {
  if (length(resultados) == 0) {
    cat("❌ No se pudieron obtener datos de ningún mercado\n")
    return()
  }
  
  cat("\n📊 RESUMEN DE OPORTUNIDADES\n")
  cat("===========================\n")
  
  # Separar por calidad de señal
  señales_alta <- Filter(function(r) r$señal$calidad == "ALTA", resultados)
  señales_media <- Filter(function(r) r$señal$calidad == "MEDIA", resultados)
  señales_baja <- Filter(function(r) r$señal$calidad == "BAJA", resultados)
  
  cat("🚀 Señales ALTA calidad:", length(señales_alta), "\n")
  cat("📈 Señales MEDIA calidad:", length(señales_media), "\n")
  cat("📊 Señales BAJA calidad:", length(señales_baja), "\n\n")
  
  # Mostrar mejores oportunidades
  if (length(señales_alta) > 0) {
    cat("🏆 MEJORES OPORTUNIDADES:\n")
    cat("=========================\n")
    
    for (resultado in señales_alta) {
      mostrar_oportunidad(resultado)
    }
  } else if (length(señales_media) > 0) {
    cat("📈 OPORTUNIDADES MEDIAS:\n")
    cat("========================\n")
    
    for (resultado in head(señales_media, 2)) {
      mostrar_oportunidad(resultado)
    }
  } else {
    cat("⏸️ NO HAY SEÑALES CLARAS HOY\n")
    cat("📊 Estado de los mercados:\n\n")
    
    for (resultado in resultados) {
      cat("•", resultado$nombre, ":", resultado$señal$tipo, 
          "| RSI:", round(resultado$rsi, 2),
          "| Calidad:", resultado$señal$calidad, "\n")
    }
  }
  
  # Análisis de correlaciones
  cat("\n🔗 ANÁLISIS DE CORRELACIONES:\n")
  cat("=============================\n")
  analizar_correlaciones(resultados)
  
  # Recomendaciones finales
  cat("\n💡 RECOMENDACIONES:\n")
  cat("===================\n")
  dar_recomendaciones_finales(resultados)
}

mostrar_oportunidad <- function(resultado) {
  niveles <- calcular_niveles_integrados(resultado)
  
  cat("📈", resultado$nombre, "(", resultado$tipo, ") -", resultado$señal$tipo, "\n")
  cat("   Precio actual:", round(resultado$precio, 2), "\n")
  cat("   RSI:", round(resultado$rsi, 2), "\n")
  cat("   Volumen factor:", round(resultado$volumen_factor, 2), "\n")
  cat("   Calidad:", resultado$señal$calidad, "\n")
  
  cat("   💰 NIVELES DE TRADING:\n")
  cat("      Contratos:", niveles$num_contratos, "\n")
  cat("      Stop Loss:", round(niveles$stop_loss, 2), "\n")
  cat("      Take Profit:", round(niveles$take_profit, 2), "\n")
  cat("      Margen requerido:", round(niveles$margen_requerido, 0), "€\n")
  cat("      Riesgo:", round(niveles$riesgo_euros, 2), "€\n")
  cat("      Ganancia potencial:", round(niveles$ganancia_potencial, 2), "€\n")
  cat("      Ratio R:B:", round(niveles$ratio_rb, 2), ":1\n\n")
}

analizar_correlaciones <- function(resultados) {
  señales_compra <- Filter(function(r) r$señal$tipo == "COMPRA", resultados)
  
  if (length(señales_compra) > 1) {
    cat("⚠️ Múltiples señales de compra detectadas\n")
    cat("💡 Considerar correlaciones entre mercados:\n")
    cat("- Oro vs Índices: Correlación negativa típica\n")
    cat("- Petróleo vs Oro: Correlación positiva en crisis\n")
    cat("- S&P vs Nasdaq: Correlación alta positiva\n")
    cat("🎯 Recomendación: Diversificar entre tipos de activos\n")
  } else {
    cat("✅ Baja correlación entre señales - Diversificación adecuada\n")
  }
}

dar_recomendaciones_finales <- function(resultados) {
  señales_alta <- Filter(function(r) r$señal$calidad == "ALTA", resultados)
  
  if (length(señales_alta) > 0) {
    cat("✅ Operar señales de ALTA calidad prioritariamente\n")
    cat("📊 Verificar márgenes disponibles antes de operar\n")
    cat("⏰ Considerar horarios de mayor liquidez\n")
    cat("🛡️ Usar stops estrictos por el apalancamiento\n")
  } else {
    cat("⏸️ Esperar mejores oportunidades\n")
    cat("🔄 Repetir escaneo en 2-4 horas\n")
    cat("📰 Revisar calendario económico\n")
  }
  
  cat("💰 Capital mínimo recomendado: €25,000\n")
  cat("🎯 Máximo 3 posiciones simultáneas\n")
  cat("📈 Enfocarse en 1-2 mercados para especializarse\n")
}

# =============================================================================
# EJECUCIÓN PRINCIPAL
# =============================================================================

escanear_oro_futuros()

cat("\n🥇📈 SCANNER ORO Y FUTUROS COMPLETADO\n")
cat("====================================\n")
cat("💡 Ejecutar 2-3 veces por día en horarios de alta liquidez\n")
cat("⚠️ Los futuros requieren gestión de riesgo estricta\n")
cat("📅 Verificar fechas de vencimiento de contratos\n")
