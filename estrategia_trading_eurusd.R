# =============================================================================
# ESTRATEGIA DE TRADING: CRUCE DE MEDIAS MÓVILES + RSI
# =============================================================================
# Estrategia: Combinación de señales técnicas para EUR/USD
# - Entrada: MA50 cruza por encima de MA200 + RSI > 50
# - Salida: RSI >= 70 o precio < MA50
# - Stop Loss: 2% por debajo del precio de entrada
# - Take Profit: 5% por encima del precio de entrada
# =============================================================================

# Cargar librerías necesarias
library(quantmod)      # Para obtener datos financieros
library(TTR)          # Para indicadores técnicos
library(ggplot2)      # Para gráficos
library(dplyr)        # Para manipulación de datos
library(gridExtra)    # Para múltiples gráficos
library(scales)       # Para formateo de escalas

# Configuración inicial
cat("=== INICIANDO ESTRATEGIA DE TRADING ===\n")
cat("Símbolo: EUR/USD\n")
cat("Período: Últimos 2 años\n")
cat("Estrategia: Cruce MA + RSI\n\n")

# =============================================================================
# 1. OBTENCIÓN Y PREPARACIÓN DE DATOS
# =============================================================================

# Función para obtener datos de EUR/USD
obtener_datos_eurusd <- function() {
  cat("Descargando datos históricos de EUR/USD...\n")
  
  # Calcular fechas (últimos 2 años)
  fecha_fin <- Sys.Date()
  fecha_inicio <- fecha_fin - 730  # Aproximadamente 2 años
  
  # Descargar datos de EUR/USD desde Yahoo Finance
  # Nota: EUR/USD se representa como EURUSD=X en Yahoo Finance
  tryCatch({
    datos <- getSymbols("EURUSD=X", 
                       src = "yahoo",
                       from = fecha_inicio,
                       to = fecha_fin,
                       auto.assign = FALSE)
    
    # Renombrar columnas para facilitar el trabajo
    colnames(datos) <- c("Open", "High", "Low", "Close", "Volume", "Adjusted")
    
    cat("✓ Datos descargados exitosamente\n")
    cat("Período:", as.character(fecha_inicio), "a", as.character(fecha_fin), "\n")
    cat("Número de observaciones:", nrow(datos), "\n\n")
    
    return(datos)
    
  }, error = function(e) {
    cat("Error al descargar datos:", e$message, "\n")
    cat("Generando datos sintéticos para demostración...\n")
    
    # Generar datos sintéticos si falla la descarga
    fechas <- seq(from = fecha_inicio, to = fecha_fin, by = "day")
    fechas <- fechas[weekdays(fechas) %in% c("Monday", "Tuesday", "Wednesday", "Thursday", "Friday")]
    
    set.seed(123)
    precio_inicial <- 1.1000
    n <- length(fechas)
    
    # Simular precios con random walk
    retornos <- rnorm(n, mean = 0.0001, sd = 0.008)
    precios <- precio_inicial * cumprod(1 + retornos)
    
    # Crear datos OHLC sintéticos
    datos_sinteticos <- xts(
      data.frame(
        Open = precios * runif(n, 0.999, 1.001),
        High = precios * runif(n, 1.001, 1.005),
        Low = precios * runif(n, 0.995, 0.999),
        Close = precios,
        Volume = sample(1000:5000, n, replace = TRUE),
        Adjusted = precios
      ),
      order.by = fechas
    )
    
    cat("✓ Datos sintéticos generados\n\n")
    return(datos_sinteticos)
  })
}

# Obtener los datos
datos_eurusd <- obtener_datos_eurusd()

# =============================================================================
# 2. CÁLCULO DE INDICADORES TÉCNICOS
# =============================================================================

calcular_indicadores <- function(datos) {
  cat("Calculando indicadores técnicos...\n")
  
  # Extraer precios de cierre
  precios <- Cl(datos)
  
  # Calcular Medias Móviles
  ma_50 <- SMA(precios, n = 50)   # Media móvil de 50 días
  ma_200 <- SMA(precios, n = 200) # Media móvil de 200 días
  
  # Calcular RSI (Relative Strength Index)
  rsi <- RSI(precios, n = 14)     # RSI de 14 períodos
  
  # Combinar todos los datos en un dataframe
  datos_completos <- data.frame(
    Fecha = index(datos),
    Precio = as.numeric(precios),
    MA_50 = as.numeric(ma_50),
    MA_200 = as.numeric(ma_200),
    RSI = as.numeric(rsi),
    High = as.numeric(Hi(datos)),
    Low = as.numeric(Lo(datos)),
    Open = as.numeric(Op(datos))
  )
  
  # Eliminar filas con valores NA (primeros 200 días)
  datos_completos <- datos_completos[complete.cases(datos_completos), ]
  
  cat("✓ Indicadores calculados\n")
  cat("Datos válidos para análisis:", nrow(datos_completos), "días\n\n")
  
  return(datos_completos)
}

# Calcular indicadores
datos_con_indicadores <- calcular_indicadores(datos_eurusd)

# =============================================================================
# 3. IMPLEMENTACIÓN DE LA ESTRATEGIA DE TRADING
# =============================================================================

implementar_estrategia <- function(datos) {
  cat("Implementando estrategia de trading...\n")
  
  # Inicializar variables para el backtesting
  posicion_abierta <- FALSE
  precio_entrada <- 0
  stop_loss <- 0
  take_profit <- 0
  operaciones <- data.frame()
  
  # Parámetros de la estrategia
  stop_loss_pct <- 0.02  # 2%
  take_profit_pct <- 0.05 # 5%
  
  # Recorrer cada día para buscar señales
  for (i in 2:nrow(datos)) {
    fecha_actual <- datos$Fecha[i]
    precio_actual <- datos$Precio[i]
    ma_50_actual <- datos$MA_50[i]
    ma_200_actual <- datos$MA_200[i]
    ma_50_anterior <- datos$MA_50[i-1]
    ma_200_anterior <- datos$MA_200[i-1]
    rsi_actual <- datos$RSI[i]
    
    # CONDICIONES DE ENTRADA
    if (!posicion_abierta) {
      # Verificar cruce alcista de medias móviles
      cruce_alcista <- (ma_50_anterior <= ma_200_anterior) && (ma_50_actual > ma_200_actual)
      
      # Verificar RSI > 50
      rsi_favorable <- rsi_actual > 50
      
      # Si se cumplen ambas condiciones, abrir posición
      if (cruce_alcista && rsi_favorable && !is.na(rsi_actual)) {
        posicion_abierta <- TRUE
        precio_entrada <- precio_actual
        stop_loss <- precio_entrada * (1 - stop_loss_pct)
        take_profit <- precio_entrada * (1 + take_profit_pct)
        
        cat("📈 ENTRADA - Fecha:", as.character(fecha_actual), 
            "Precio:", round(precio_entrada, 5), 
            "RSI:", round(rsi_actual, 2), "\n")
      }
    }
    
    # CONDICIONES DE SALIDA
    if (posicion_abierta) {
      salida <- FALSE
      motivo_salida <- ""
      
      # Condición 1: RSI >= 70 (sobrecompra)
      if (rsi_actual >= 70) {
        salida <- TRUE
        motivo_salida <- "RSI_Sobrecompra"
      }
      
      # Condición 2: Precio < MA50
      if (precio_actual < ma_50_actual) {
        salida <- TRUE
        motivo_salida <- "Precio_Bajo_MA50"
      }
      
      # Condición 3: Stop Loss
      if (precio_actual <= stop_loss) {
        salida <- TRUE
        motivo_salida <- "Stop_Loss"
      }
      
      # Condición 4: Take Profit
      if (precio_actual >= take_profit) {
        salida <- TRUE
        motivo_salida <- "Take_Profit"
      }
      
      # Si hay señal de salida, cerrar posición
      if (salida) {
        precio_salida <- precio_actual
        rendimiento <- (precio_salida - precio_entrada) / precio_entrada
        rendimiento_pct <- rendimiento * 100
        
        # Registrar la operación
        nueva_operacion <- data.frame(
          Entrada_Fecha = fecha_actual,
          Entrada_Precio = precio_entrada,
          Salida_Fecha = fecha_actual,
          Salida_Precio = precio_salida,
          Rendimiento_Pct = rendimiento_pct,
          Motivo_Salida = motivo_salida,
          Ganadora = rendimiento > 0
        )
        
        operaciones <- rbind(operaciones, nueva_operacion)
        
        cat("📉 SALIDA - Fecha:", as.character(fecha_actual), 
            "Precio:", round(precio_salida, 5), 
            "Rendimiento:", round(rendimiento_pct, 2), "%",
            "Motivo:", motivo_salida, "\n")
        
        # Resetear variables
        posicion_abierta <- FALSE
        precio_entrada <- 0
        stop_loss <- 0
        take_profit <- 0
      }
    }
  }
  
  cat("\n✓ Estrategia implementada\n")
  cat("Total de operaciones:", nrow(operaciones), "\n\n")
  
  return(operaciones)
}

# Ejecutar la estrategia
resultados_operaciones <- implementar_estrategia(datos_con_indicadores)

# =============================================================================
# 4. ANÁLISIS DE RESULTADOS DEL BACKTESTING
# =============================================================================

analizar_resultados <- function(operaciones) {
  cat("=== ANÁLISIS DE RESULTADOS ===\n")

  if (nrow(operaciones) == 0) {
    cat("❌ No se encontraron operaciones en el período analizado\n")
    return(list())
  }

  # Estadísticas básicas
  total_operaciones <- nrow(operaciones)
  operaciones_ganadoras <- sum(operaciones$Ganadora)
  operaciones_perdedoras <- total_operaciones - operaciones_ganadoras
  tasa_exito <- (operaciones_ganadoras / total_operaciones) * 100

  # Rendimientos
  rendimiento_promedio <- mean(operaciones$Rendimiento_Pct)
  rendimiento_total <- sum(operaciones$Rendimiento_Pct)
  mejor_operacion <- max(operaciones$Rendimiento_Pct)
  peor_operacion <- min(operaciones$Rendimiento_Pct)

  # Calcular drawdown máximo
  rendimientos_acumulados <- cumsum(operaciones$Rendimiento_Pct)
  picos <- cummax(rendimientos_acumulados)
  drawdowns <- rendimientos_acumulados - picos
  drawdown_maximo <- min(drawdowns)

  # Análisis por motivo de salida
  motivos_salida <- table(operaciones$Motivo_Salida)

  # Mostrar resultados
  cat("\n📊 ESTADÍSTICAS GENERALES:\n")
  cat("Total de operaciones:", total_operaciones, "\n")
  cat("Operaciones ganadoras:", operaciones_ganadoras, "\n")
  cat("Operaciones perdedoras:", operaciones_perdedoras, "\n")
  cat("Tasa de éxito:", round(tasa_exito, 2), "%\n")

  cat("\n💰 RENDIMIENTOS:\n")
  cat("Rendimiento promedio por operación:", round(rendimiento_promedio, 2), "%\n")
  cat("Rendimiento total acumulado:", round(rendimiento_total, 2), "%\n")
  cat("Mejor operación:", round(mejor_operacion, 2), "%\n")
  cat("Peor operación:", round(peor_operacion, 2), "%\n")
  cat("Drawdown máximo:", round(drawdown_maximo, 2), "%\n")

  cat("\n🚪 MOTIVOS DE SALIDA:\n")
  for (motivo in names(motivos_salida)) {
    cat(motivo, ":", motivos_salida[motivo], "operaciones\n")
  }

  # Crear lista de resultados para retornar
  resultados <- list(
    total_operaciones = total_operaciones,
    operaciones_ganadoras = operaciones_ganadoras,
    tasa_exito = tasa_exito,
    rendimiento_total = rendimiento_total,
    rendimiento_promedio = rendimiento_promedio,
    drawdown_maximo = drawdown_maximo,
    mejor_operacion = mejor_operacion,
    peor_operacion = peor_operacion,
    motivos_salida = motivos_salida,
    rendimientos_acumulados = rendimientos_acumulados
  )

  return(resultados)
}

# Analizar resultados
analisis <- analizar_resultados(resultados_operaciones)

# =============================================================================
# 5. VISUALIZACIONES Y GRÁFICOS
# =============================================================================

crear_graficos <- function(datos, operaciones, analisis) {
  cat("\n📈 Generando gráficos...\n")

  # Gráfico 1: Precio y Medias Móviles
  grafico_precio <- ggplot(datos, aes(x = Fecha)) +
    geom_line(aes(y = Precio, color = "Precio EUR/USD"), size = 0.8) +
    geom_line(aes(y = MA_50, color = "MA 50"), size = 0.7) +
    geom_line(aes(y = MA_200, color = "MA 200"), size = 0.7) +
    scale_color_manual(values = c("Precio EUR/USD" = "black",
                                  "MA 50" = "blue",
                                  "MA 200" = "red")) +
    labs(title = "EUR/USD - Precio y Medias Móviles",
         subtitle = "Estrategia: Cruce de MA50 y MA200",
         x = "Fecha",
         y = "Precio",
         color = "Indicador") +
    theme_minimal() +
    theme(legend.position = "bottom",
          plot.title = element_text(size = 14, face = "bold"),
          plot.subtitle = element_text(size = 12))

  # Añadir puntos de entrada si hay operaciones
  if (nrow(operaciones) > 0) {
    # Crear dataframe con puntos de entrada
    puntos_entrada <- data.frame(
      Fecha = operaciones$Entrada_Fecha,
      Precio = operaciones$Entrada_Precio
    )

    grafico_precio <- grafico_precio +
      geom_point(data = puntos_entrada,
                aes(x = Fecha, y = Precio),
                color = "green", size = 3, shape = 24, fill = "green") +
      annotate("text", x = max(datos$Fecha), y = max(datos$Precio),
               label = "▲ Puntos de Entrada", color = "green", hjust = 1)
  }

  # Gráfico 2: RSI
  grafico_rsi <- ggplot(datos, aes(x = Fecha, y = RSI)) +
    geom_line(color = "purple", size = 0.8) +
    geom_hline(yintercept = 70, color = "red", linetype = "dashed", alpha = 0.7) +
    geom_hline(yintercept = 50, color = "blue", linetype = "dashed", alpha = 0.7) +
    geom_hline(yintercept = 30, color = "green", linetype = "dashed", alpha = 0.7) +
    annotate("text", x = max(datos$Fecha), y = 75, label = "Sobrecompra (70)",
             color = "red", hjust = 1, size = 3) +
    annotate("text", x = max(datos$Fecha), y = 55, label = "Neutral (50)",
             color = "blue", hjust = 1, size = 3) +
    annotate("text", x = max(datos$Fecha), y = 25, label = "Sobreventa (30)",
             color = "green", hjust = 1, size = 3) +
    labs(title = "Índice de Fuerza Relativa (RSI)",
         subtitle = "Niveles clave: 30 (sobreventa), 50 (neutral), 70 (sobrecompra)",
         x = "Fecha",
         y = "RSI") +
    ylim(0, 100) +
    theme_minimal() +
    theme(plot.title = element_text(size = 14, face = "bold"),
          plot.subtitle = element_text(size = 12))

  # Gráfico 3: Rendimientos Acumulados (si hay operaciones)
  if (nrow(operaciones) > 0 && length(analisis$rendimientos_acumulados) > 0) {
    datos_rendimientos <- data.frame(
      Operacion = 1:length(analisis$rendimientos_acumulados),
      Rendimiento_Acumulado = analisis$rendimientos_acumulados
    )

    grafico_rendimientos <- ggplot(datos_rendimientos, aes(x = Operacion, y = Rendimiento_Acumulado)) +
      geom_line(color = "darkgreen", size = 1.2) +
      geom_point(color = "darkgreen", size = 2) +
      geom_hline(yintercept = 0, color = "black", linetype = "dashed", alpha = 0.5) +
      labs(title = "Evolución de Rendimientos Acumulados",
           subtitle = paste("Rendimiento Total:", round(analisis$rendimiento_total, 2), "%"),
           x = "Número de Operación",
           y = "Rendimiento Acumulado (%)") +
      theme_minimal() +
      theme(plot.title = element_text(size = 14, face = "bold"),
            plot.subtitle = element_text(size = 12))
  } else {
    grafico_rendimientos <- ggplot() +
      annotate("text", x = 0.5, y = 0.5, label = "No hay operaciones para mostrar", size = 6) +
      theme_void()
  }

  # Gráfico 4: Distribución de Rendimientos por Operación
  if (nrow(operaciones) > 0) {
    grafico_distribucion <- ggplot(operaciones, aes(x = Rendimiento_Pct, fill = Ganadora)) +
      geom_histogram(bins = 15, alpha = 0.7, color = "black") +
      scale_fill_manual(values = c("TRUE" = "green", "FALSE" = "red"),
                       labels = c("TRUE" = "Ganadora", "FALSE" = "Perdedora")) +
      geom_vline(xintercept = 0, color = "black", linetype = "dashed", size = 1) +
      labs(title = "Distribución de Rendimientos por Operación",
           subtitle = paste("Tasa de Éxito:", round(analisis$tasa_exito, 1), "%"),
           x = "Rendimiento (%)",
           y = "Frecuencia",
           fill = "Tipo de Operación") +
      theme_minimal() +
      theme(legend.position = "bottom",
            plot.title = element_text(size = 14, face = "bold"),
            plot.subtitle = element_text(size = 12))
  } else {
    grafico_distribucion <- ggplot() +
      annotate("text", x = 0.5, y = 0.5, label = "No hay operaciones para mostrar", size = 6) +
      theme_void()
  }

  # Mostrar todos los gráficos
  grid.arrange(grafico_precio, grafico_rsi,
               grafico_rendimientos, grafico_distribucion,
               ncol = 2, nrow = 2)

  cat("✓ Gráficos generados exitosamente\n")
}

# Crear visualizaciones
crear_graficos(datos_con_indicadores, resultados_operaciones, analisis)

# =============================================================================
# 6. FUNCIONES ADICIONALES DE ANÁLISIS
# =============================================================================

# Función para crear tabla resumen de operaciones
crear_tabla_operaciones <- function(operaciones) {
  if (nrow(operaciones) == 0) {
    cat("No hay operaciones para mostrar en tabla\n")
    return()
  }

  cat("\n📋 TABLA DETALLADA DE OPERACIONES:\n")
  cat(paste(rep("=", 80), collapse = ""), "\n")

  # Formatear tabla para mostrar
  tabla_formato <- operaciones %>%
    mutate(
      Entrada_Fecha = format(Entrada_Fecha, "%Y-%m-%d"),
      Salida_Fecha = format(Salida_Fecha, "%Y-%m-%d"),
      Entrada_Precio = round(Entrada_Precio, 5),
      Salida_Precio = round(Salida_Precio, 5),
      Rendimiento_Pct = round(Rendimiento_Pct, 2),
      Resultado = ifelse(Ganadora, "✓ GANADORA", "✗ PERDEDORA")
    ) %>%
    select(Entrada_Fecha, Entrada_Precio, Salida_Fecha, Salida_Precio,
           Rendimiento_Pct, Motivo_Salida, Resultado)

  print(tabla_formato)
  cat(paste(rep("=", 80), collapse = ""), "\n")
}

# Función para análisis de riesgo-beneficio
analisis_riesgo_beneficio <- function(operaciones) {
  if (nrow(operaciones) == 0) {
    return()
  }

  cat("\n⚖️ ANÁLISIS RIESGO-BENEFICIO:\n")

  # Separar operaciones ganadoras y perdedoras
  ganadoras <- operaciones[operaciones$Ganadora == TRUE, ]
  perdedoras <- operaciones[operaciones$Ganadora == FALSE, ]

  if (nrow(ganadoras) > 0) {
    ganancia_promedio <- mean(ganadoras$Rendimiento_Pct)
    cat("Ganancia promedio por operación ganadora:", round(ganancia_promedio, 2), "%\n")
  }

  if (nrow(perdedoras) > 0) {
    perdida_promedio <- mean(perdedoras$Rendimiento_Pct)
    cat("Pérdida promedio por operación perdedora:", round(perdida_promedio, 2), "%\n")

    if (nrow(ganadoras) > 0) {
      ratio_riesgo_beneficio <- abs(ganancia_promedio / perdida_promedio)
      cat("Ratio Riesgo-Beneficio:", round(ratio_riesgo_beneficio, 2), ":1\n")
    }
  }

  # Análisis de rachas
  rachas <- rle(operaciones$Ganadora)
  racha_ganadora_max <- max(rachas$lengths[rachas$values == TRUE], na.rm = TRUE)
  racha_perdedora_max <- max(rachas$lengths[rachas$values == FALSE], na.rm = TRUE)

  if (!is.infinite(racha_ganadora_max)) {
    cat("Racha ganadora máxima:", racha_ganadora_max, "operaciones\n")
  }
  if (!is.infinite(racha_perdedora_max)) {
    cat("Racha perdedora máxima:", racha_perdedora_max, "operaciones\n")
  }
}

# Función para recomendaciones basadas en resultados
generar_recomendaciones <- function(analisis, operaciones) {
  cat("\n💡 RECOMENDACIONES Y CONCLUSIONES:\n")
  cat(paste(rep("=", 50), collapse = ""), "\n")

  if (length(analisis) == 0 || nrow(operaciones) == 0) {
    cat("❌ No hay suficientes datos para generar recomendaciones\n")
    return()
  }

  # Evaluar tasa de éxito
  if (analisis$tasa_exito >= 60) {
    cat("✅ TASA DE ÉXITO: Excelente (", round(analisis$tasa_exito, 1), "%)\n")
  } else if (analisis$tasa_exito >= 50) {
    cat("⚠️ TASA DE ÉXITO: Aceptable (", round(analisis$tasa_exito, 1), "%)\n")
  } else {
    cat("❌ TASA DE ÉXITO: Baja (", round(analisis$tasa_exito, 1), "%)\n")
    cat("   Recomendación: Revisar criterios de entrada\n")
  }

  # Evaluar rendimiento total
  if (analisis$rendimiento_total > 10) {
    cat("✅ RENDIMIENTO: Muy bueno (", round(analisis$rendimiento_total, 1), "%)\n")
  } else if (analisis$rendimiento_total > 0) {
    cat("⚠️ RENDIMIENTO: Positivo pero modesto (", round(analisis$rendimiento_total, 1), "%)\n")
  } else {
    cat("❌ RENDIMIENTO: Negativo (", round(analisis$rendimiento_total, 1), "%)\n")
    cat("   Recomendación: La estrategia necesita optimización\n")
  }

  # Evaluar drawdown
  if (abs(analisis$drawdown_maximo) < 5) {
    cat("✅ DRAWDOWN: Bajo riesgo (", round(analisis$drawdown_maximo, 1), "%)\n")
  } else if (abs(analisis$drawdown_maximo) < 10) {
    cat("⚠️ DRAWDOWN: Riesgo moderado (", round(analisis$drawdown_maximo, 1), "%)\n")
  } else {
    cat("❌ DRAWDOWN: Alto riesgo (", round(analisis$drawdown_maximo, 1), "%)\n")
    cat("   Recomendación: Considerar reducir el tamaño de posición\n")
  }

  # Análisis de motivos de salida
  if ("Take_Profit" %in% names(analisis$motivos_salida)) {
    tp_count <- analisis$motivos_salida["Take_Profit"]
    cat("📈 Take Profit alcanzado en", tp_count, "operaciones\n")
  }

  if ("Stop_Loss" %in% names(analisis$motivos_salida)) {
    sl_count <- analisis$motivos_salida["Stop_Loss"]
    cat("📉 Stop Loss activado en", sl_count, "operaciones\n")
  }

  cat("\n🎯 SUGERENCIAS DE OPTIMIZACIÓN:\n")
  cat("1. Considerar ajustar los períodos de las medias móviles\n")
  cat("2. Evaluar diferentes niveles de RSI para entrada\n")
  cat("3. Probar con diferentes ratios de Stop Loss / Take Profit\n")
  cat("4. Añadir filtros adicionales (volumen, volatilidad)\n")
  cat("5. Considerar el contexto de mercado (tendencia general)\n")
}

# =============================================================================
# 7. EJECUCIÓN PRINCIPAL Y RESUMEN FINAL
# =============================================================================

# Ejecutar análisis adicionales
cat(paste(rep("\n", 2), collapse = ""), paste(rep("=", 80), collapse = ""), "\n")
crear_tabla_operaciones(resultados_operaciones)
analisis_riesgo_beneficio(resultados_operaciones)
generar_recomendaciones(analisis, resultados_operaciones)

# Resumen final
cat("\n🏁 RESUMEN EJECUTIVO DE LA ESTRATEGIA\n")
cat(paste(rep("=", 80), collapse = ""), "\n")
cat("Estrategia: Cruce de Medias Móviles (50/200) + RSI\n")
cat("Símbolo: EUR/USD\n")
cat("Período analizado:", nrow(datos_con_indicadores), "días de trading\n")

if (length(analisis) > 0 && analisis$total_operaciones > 0) {
  cat("Total de señales generadas:", analisis$total_operaciones, "\n")
  cat("Rendimiento total:", round(analisis$rendimiento_total, 2), "%\n")
  cat("Tasa de éxito:", round(analisis$tasa_exito, 1), "%\n")
  cat("Drawdown máximo:", round(analisis$drawdown_maximo, 2), "%\n")

  # Calcular rendimiento anualizado aproximado
  dias_trading <- nrow(datos_con_indicadores)
  rendimiento_anualizado <- (analisis$rendimiento_total / dias_trading) * 252
  cat("Rendimiento anualizado estimado:", round(rendimiento_anualizado, 2), "%\n")
} else {
  cat("❌ No se generaron señales de trading en el período analizado\n")
  cat("Esto puede deberse a:\n")
  cat("- Mercado sin tendencias claras\n")
  cat("- Criterios de entrada muy restrictivos\n")
  cat("- Período de análisis insuficiente\n")
}

cat("\n✅ Análisis completado exitosamente\n")
cat(paste(rep("=", 80), collapse = ""), "\n")

# Nota: Para ejecutar este script, asegúrate de tener instaladas las librerías:
# install.packages(c("quantmod", "TTR", "ggplot2", "dplyr", "gridExtra", "scales"))
